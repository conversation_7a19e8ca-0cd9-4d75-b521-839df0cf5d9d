<?xml version="1.0"?>

<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
  <xacro:macro name="foot_force_sensor" params="name suffix">
    <gazebo reference="${name}_${suffix}">
      <!-- Enable feedback for this joint -->
      <provide_feedback>true</provide_feedback>
      <!-- Prevent ros2_control from lumping this fixed joint with others -->
      <disableFixedJointLumping>true</disableFixedJointLumping>
      <sensor name="${name}_foot_force" type="force_torque">
        <always_on>1</always_on>
        <update_rate>1000</update_rate>
        <visualize>true</visualize>
        <topic>${name}_foot_force</topic>
        <force_torque>
          <frame>child</frame>
        </force_torque>
      </sensor>
    </gazebo>
  </xacro:macro>
</robot>