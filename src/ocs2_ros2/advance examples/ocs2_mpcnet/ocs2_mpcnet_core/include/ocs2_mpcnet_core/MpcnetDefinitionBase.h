/******************************************************************************
Copyright (c) 2022, <PERSON>bod Far<PERSON>dian. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

 * Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

 * Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

 * Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONT<PERSON>BUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
******************************************************************************/

#pragma once

#include <ocs2_core/reference/ModeSchedule.h>
#include <ocs2_core/reference/TargetTrajectories.h>

namespace ocs2::mpcnet {
    /**
    * Base class for MPC-Net definitions.
    */
    class MpcnetDefinitionBase {
    public:
        /**
         * Default constructor.
         */
        MpcnetDefinitionBase() = default;

        /**
         * Default destructor.
         */
        virtual ~MpcnetDefinitionBase() = default;

        /**
         * Deleted copy constructor.
         */
        MpcnetDefinitionBase(const MpcnetDefinitionBase &) = delete;

        /**
         * Deleted copy assignment.
         */
        MpcnetDefinitionBase &operator=(const MpcnetDefinitionBase &) = delete;

        /**
         * Get the observation.
         * @note The observation o is the input to the policy.
         * @param[in] t : Absolute time.
         * @param[in] x : Robot state.
         * @param[in] modeSchedule : Mode schedule.
         * @param[in] targetTrajectories : Target trajectories.
         * @return The observation.
         */
        virtual vector_t getObservation(scalar_t t, const vector_t &x, const ModeSchedule &modeSchedule,
                                        const TargetTrajectories &targetTrajectories) = 0;

        /**
         * Get the action transformation.
         * @note Used for computing the control input u = A * a + b from the action a predicted by the policy.
         * @param[in] t : Absolute time.
         * @param[in] x : Robot state.
         * @param[in] modeSchedule : Mode schedule.
         * @param[in] targetTrajectories : Target trajectories.
         * @return The action transformation pair {A, b}.
         */
        virtual std::pair<matrix_t, vector_t> getActionTransformation(scalar_t t, const vector_t &x,
                                                                      const ModeSchedule &modeSchedule,
                                                                      const TargetTrajectories &targetTrajectories) = 0;

        /**
         * Check if the tuple (t, x, modeSchedule, targetTrajectories) is valid.
         * @note E.g., check if the state diverged or if tracking is poor.
         * @param[in] t : Absolute time.
         * @param[in] x : Robot state.
         * @param[in] modeSchedule : Mode schedule.
         * @param[in] targetTrajectories : Target trajectories.
         * @return True if valid.
         */
        virtual bool isValid(scalar_t t, const vector_t &x, const ModeSchedule &modeSchedule,
                             const TargetTrajectories &targetTrajectories) = 0;
    };
}
