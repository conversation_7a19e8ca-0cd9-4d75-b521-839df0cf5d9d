<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="b2_description">
  <link
    name="base_link">
    <inertial>
      <origin
        xyz="0.000458 0.005261 0.000665"
        rpy="0 0 0" />
      <mass
        value="35.86" />
      <inertia
        ixx="0.27466"
        ixy="-0.000622"
        ixz="0.00315"
        iyy="1.0618"
        iyz="-0.00139"
        izz="1.1825" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/base_link.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
        <geometry>
          <box size="0.5 0.28 0.15" />
        </geometry>
    </collision>
  </link>
  <link
    name="lidar_link">
    <inertial>
      <origin
        xyz="0.34218 0 0.17851"
        rpy="0 0 0" />
      <mass
        value="4.19E-15" />
      <inertia
        ixx="1.68E-27"
        ixy="-3.2E-45"
        ixz="7.68E-44"
        iyy="1.68E-27"
        iyz="-9.26E-47"
        izz="1.68E-27" />
    </inertial>
    <collision>
      <origin
        xyz="0 0 -0.02"
        rpy="0 0 0" />
      <geometry>
        <cylinder length="0.16" radius="0.076" />
        <!-- <mesh
          filename="package://b2_description/meshes/lidar_link.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="joint_lidar"
    type="fixed">
    <origin
      xyz="0.34218 0 0.17851"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="lidar_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="f_dc_link">
    <inertial>
      <origin
        xyz="3.47E-18 0 5.55E-17"
        rpy="0 0 0" />
      <mass
        value="4.19E-15" />
      <inertia
        ixx="1.68E-27"
        ixy="-9.81E-45"
        ixz="-2.54E-44"
        iyy="1.68E-27"
        iyz="0"
        izz="1.68E-27" />
    </inertial>
  </link>
  <joint
    name="joint_f_dc"
    type="fixed">
    <origin
      xyz="0.41251 0.024997 0.04765"
      rpy="-2.3562 0 -1.5708" />
    <parent
      link="base_link" />
    <child
      link="f_dc_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="r_dc_link">
    <inertial>
      <origin
        xyz="-7.46E-16 -3.33E-16 -1.11E-15"
        rpy="0 0 0" />
      <mass
        value="4.19E-15" />
      <inertia
        ixx="1.68E-27"
        ixy="-4.31E-44"
        ixz="2.48E-45"
        iyy="1.68E-27"
        iyz="0"
        izz="1.68E-27" />
    </inertial>
  </link>
  <joint
    name="joint_r_dc"
    type="fixed">
    <origin
      xyz="-0.4081 -0.025 0.024861"
      rpy="-2.3557 0 1.5708" />
    <parent
      link="base_link" />
    <child
      link="r_dc_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="imu_link">
    <inertial>
      <origin
        xyz="-2.02E-17 1.73E-17 -1.39E-17"
        rpy="0 0 0" />
      <mass
        value="4.19E-15" />
      <inertia
        ixx="1.68E-27"
        ixy="1.84E-44"
        ixz="-7.35E-44"
        iyy="1.68E-27"
        iyz="-3.67E-44"
        izz="1.68E-27" />
    </inertial>
  </link>
  <joint
    name="joint_imu"
    type="fixed">
    <origin
      xyz="0 -0.02341 0.04927"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="imu_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="head_Link">
    <inertial>
      <origin
        xyz="0.33989 -0.000168 0.12029"
        rpy="0 0 0" />
      <mass
        value="3.6885" />
      <inertia
        ixx="0.026455"
        ixy="3.15E-05"
        ixz="0.00166"
        iyy="0.029221"
        iyz="-3.27E-05"
        izz="0.010918" />
    </inertial>
    <collision>
      <origin
        xyz="0.41 0 0.005"
        rpy="0 0 0" />
      <geometry>
        <box size="0.04 0.12 0.14" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint_head"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="head_Link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="tail_link">
    <inertial>
      <origin
        xyz="-0.37448 0.000488 0.006495"
        rpy="0 0 0" />
      <mass
        value="0.795" />
      <inertia
        ixx="0.0028118"
        ixy="1.95E-05"
        ixz="-7.87E-06"
        iyy="0.0047506"
        iyz="-1.57E-05"
        izz="0.0029154" />
    </inertial>
    <collision>
      <origin
        xyz="-0.405 0 0.005"
        rpy="0 0 0" />
      <geometry>
        <box size="0.025 0.12 0.14" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint_tail"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="tail_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="FL_hip">
    <inertial>
      <origin
        xyz="-0.003841 -0.009068 0"
        rpy="0 0 0" />
      <mass
        value="2.673" />
      <inertia
        ixx="0.0033188"
        ixy="7.16E-05"
        ixz="-3.77E-07"
        iyy="0.0048743"
        iyz="4E-09"
        izz="0.0037087" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/FL_hip.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0.12 0"
        rpy="1.5707963267948966 0 0" />
      <geometry>
        <cylinder length="0.05" radius="0.07" />
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_hip_joint"
    type="revolute">
    <origin
      xyz="0.3285 0.072 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="FL_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.87"
      upper="0.87"
      effort="200"
      velocity="23" />
  </joint>
  <link name="FL_hip_rotor">
    <!-- <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="FL_hip_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.20205 0.072 0"/>
    <parent link="base_link"/>
    <child link="FL_hip_rotor"/>
  </joint>
  <link
    name="FL_thigh">
    <inertial>
      <origin
        xyz="-0.006279 -0.032049 -0.057835"
        rpy="0 0 0" />
      <mass
        value="4.536" />
      <inertia
        ixx="0.062299"
        ixy="0.00087781"
        ixz="-0.0036475"
        iyy="0.061399"
        iyz="0.0083537"
        izz="0.0081997" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/FL_thigh.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="-0.025 0 -0.16"
        rpy="0 1.57 0" />
      <geometry>
        <box size="0.32 0.0445 0.054" />
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_thigh_joint"
    type="revolute">
    <origin
      xyz="0 0.11973 0"
      rpy="0 0 0" />
    <parent
      link="FL_hip" />
    <child
      link="FL_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.94"
      upper="4.69"
      effort="200"
      velocity="23" />
  </joint>
  <link name="FL_thigh_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="FL_thigh_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.00798 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh_rotor"/>
  </joint>
  <link
    name="FL_calf">
    <inertial>
      <origin
        xyz="0.012422 0 -0.12499"
        rpy="0 0 0" />
      <mass
        value="0.404" />
      <inertia
        ixx="0.01143"
        ixy="0"
        ixz="0.000643"
        iyy="0.011534"
        iyz="0"
        izz="0.000331" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/FL_calf.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.2 0" xyz="-0.002 0 -0.03"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.35 0" xyz="0.022 0 -0.1"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.53 0" xyz="0.036 0 -0.225"/>
      <geometry>
        <box size="0.15 0.0245 0.015"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 2 0" xyz="0.023 0 -0.31"/>
      <geometry>
        <box size="0.05 0.028 0.035"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_calf_joint"
    type="revolute">
    <origin
      xyz="0 -8.6984E-05 -0.35"
      rpy="0 0 0" />
    <parent
      link="FL_thigh" />
    <child
      link="FL_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.82"
      upper="-0.43"
      effort="320"
      velocity="14" />
  </joint>
  <link name="FL_calf_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="FL_calf_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.05788 0"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf_rotor"/>
  </joint>
  <link
    name="FL_foot">
    <inertial>
      <origin
        xyz="-0.006511 0 -0.010144"
        rpy="0 0 0" />
      <mass
        value="0.126" />
      <inertia
        ixx="3.8E-05"
        ixy="0"
        ixz="1.1E-05"
        iyy="4.1E-05"
        iyz="0"
        izz="4.2E-05" />
    </inertial>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="1.57 0 0" />
      <geometry>
        <sphere radius="0.032" />
      </geometry>
    </collision>
  </link>
  <joint
    name="FL_foot_joint"
    type="fixed">
    <origin
      xyz="0 0 -0.35"
      rpy="0 0 0" />
    <parent
      link="FL_calf" />
    <child
      link="FL_foot" />
  </joint>
  <link
    name="FR_hip">
    <inertial>
      <origin
        xyz="-0.009305 0.010228 0.000264"
        rpy="0 0 0" />
      <mass
        value="2.673" />
      <inertia
        ixx="0.0026431"
        ixy="-0.00019234"
        ixz="-6.76E-06"
        iyy="0.0046728"
        iyz="7.16E-06"
        izz="0.0034208" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/FR_hip.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 -0.12 0"
        rpy="1.5707963267948966 0 0" />
      <geometry>
        <cylinder length="0.05" radius="0.07" />
      </geometry>
    </collision>
  </link>
  <joint
    name="FR_hip_joint"
    type="revolute">
    <origin
      xyz="0.3285 -0.072 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="FR_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.87"
      upper="0.87"
      effort="200"
      velocity="23" />
  </joint>
  <link name="FR_hip_rotor">
    <!-- <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="FR_hip_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0.20205 -0.072 0"/>
    <parent link="base_link"/>
    <child link="FR_hip_rotor"/>
  </joint>
  <link
    name="FR_thigh">
    <inertial>
      <origin
        xyz="-0.006279 0.032049 -0.057835"
        rpy="0 0 0" />
      <mass
        value="4.536" />
      <inertia
        ixx="0.06229908"
        ixy="-0.000877808"
        ixz="-0.003647464"
        iyy="0.06139927"
        iyz="-0.008353671"
        izz="0.008199745" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/FR_thigh.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.57 0" xyz="-0.03 0 -0.2"/>
      <geometry>
        <box size="0.25 0.028 0.04"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FR_thigh_joint"
    type="revolute">
    <origin
      xyz="0 -0.11973 0"
      rpy="0 0 0" />
    <parent
      link="FR_hip" />
    <child
      link="FR_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.94"
      upper="4.69"
      effort="200"
      velocity="23" />
  </joint>
  <link name="FR_thigh_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="FR_thigh_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.00798 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh_rotor"/>
  </joint>
  <link
    name="FR_calf">
    <inertial>
      <origin
        xyz="0.012422 0 -0.12499"
        rpy="0 0 0" />
      <mass
        value="0.404" />
      <inertia
        ixx="0.01143"
        ixy="0"
        ixz="0.000643"
        iyy="0.011534"
        iyz="0"
        izz="0.000331" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/FR_calf.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.2 0" xyz="-0.002 0 -0.03"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.35 0" xyz="0.022 0 -0.1"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.53 0" xyz="0.036 0 -0.225"/>
      <geometry>
        <box size="0.15 0.0245 0.015"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 2 0" xyz="0.023 0 -0.31"/>
      <geometry>
        <box size="0.05 0.028 0.035"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="FR_calf_joint"
    type="revolute">
    <origin
      xyz="0 8.6986E-05 -0.35"
      rpy="0 0 0" />
    <parent
      link="FR_thigh" />
    <child
      link="FR_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.82"
      upper="-0.43"
      effort="320"
      velocity="14" />
  </joint>
  <link name="FR_calf_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
      <material name="green"/>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="FR_calf_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.05788 0"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf_rotor"/>
  </joint>
  <link
    name="FR_foot">
    <inertial>
      <origin
        xyz="-0.006511 0 -0.010144"
        rpy="0 0 0" />
      <mass
        value="0.126" />
      <inertia
        ixx="3.8E-05"
        ixy="0"
        ixz="1.1E-05"
        iyy="4.1E-05"
        iyz="0"
        izz="4.2E-05" />
    </inertial>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="1.57 0 0" />
      <geometry>
        <sphere radius="0.032" />
      </geometry>
    </collision>
  </link>
  <joint
    name="FR_foot_joint"
    type="fixed">
    <origin
      xyz="0 0 -0.35"
      rpy="0 0 0" />
    <parent
      link="FR_calf" />
    <child
      link="FR_foot" />
  </joint>
  <link
    name="RL_hip">
    <inertial>
      <origin
        xyz="0.009305 -0.010228 0.000264"
        rpy="0 0 0" />
      <mass
        value="2.673" />
      <inertia
        ixx="0.0026431"
        ixy="-0.00019234"
        ixz="6.76E-06"
        iyy="0.0046728"
        iyz="-7.16E-06"
        izz="0.0034208" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/RL_hip.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0.12 0"
        rpy="1.5707963267948966 0 0" />
      <geometry>
        <cylinder length="0.05" radius="0.07" />
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_hip_joint"
    type="revolute">
    <origin
      xyz="-0.3285 0.072 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="RL_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.87"
      upper="0.87"
      effort="200"
      velocity="23" />
  </joint>
  <link name="RL_hip_rotor">
    <!-- <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="RL_hip_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="-0.20205 0.072 0"/>
    <parent link="base_link"/>
    <child link="RL_hip_rotor"/>
  </joint>
  <link
    name="RL_thigh">
    <inertial>
      <origin
        xyz="-0.006279 -0.032049 -0.057835"
        rpy="0 0 0" />
      <mass
        value="4.536" />
      <inertia
        ixx="0.06229908"
        ixy="0.000877808"
        ixz="-0.003647464"
        iyy="0.06139927"
        iyz="0.008353671"
        izz="0.008199745" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/RL_thigh.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.57 0" xyz="-0.03 0 -0.2"/>
      <geometry>
        <box size="0.25 0.028 0.04"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_thigh_joint"
    type="revolute">
    <origin
      xyz="0 0.11973 0"
      rpy="0 0 0" />
    <parent
      link="RL_hip" />
    <child
      link="RL_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.94"
      upper="4.69"
      effort="200"
      velocity="23" />
  </joint>
  <link name="RL_thigh_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="RL_thigh_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.00798 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh_rotor"/>
  </joint>
  <link
    name="RL_calf">
    <inertial>
      <origin
        xyz="0.012422 0 -0.12499"
        rpy="0 0 0" />
      <mass
        value="0.404" />
      <inertia
        ixx="0.01143"
        ixy="0"
        ixz="0.000643"
        iyy="0.011534"
        iyz="0"
        izz="0.000331" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/RL_calf.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.2 0" xyz="-0.002 0 -0.03"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.35 0" xyz="0.022 0 -0.1"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.53 0" xyz="0.036 0 -0.225"/>
      <geometry>
        <box size="0.15 0.0245 0.015"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 2 0" xyz="0.023 0 -0.31"/>
      <geometry>
        <box size="0.05 0.028 0.035"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_calf_joint"
    type="revolute">
    <origin
      xyz="0 -8.6984E-05 -0.35"
      rpy="0 0 0" />
    <parent
      link="RL_thigh" />
    <child
      link="RL_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.82"
      upper="-0.43"
      effort="320"
      velocity="14" />
  </joint>
  <link name="RL_calf_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="RL_calf_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.05788 0"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf_rotor"/>
  </joint>
  <link
    name="RL_foot">
    <inertial>
      <origin
        xyz="-0.006511 0 -0.010144"
        rpy="0 0 0" />
      <mass
        value="0.126" />
      <inertia
        ixx="3.8E-05"
        ixy="0"
        ixz="1.1E-05"
        iyy="4.1E-05"
        iyz="0"
        izz="4.2E-05" />
    </inertial>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="1.57 0 0" />
      <geometry>
        <sphere radius="0.032" />
      </geometry>
    </collision>
  </link>
  <joint
    name="RL_foot_joint"
    type="fixed">
    <origin
      xyz="0 0 -0.35"
      rpy="0 0 0" />
    <parent
      link="RL_calf" />
    <child
      link="RL_foot" />
  </joint>
  <link
    name="RR_hip">
    <inertial>
      <origin
        xyz="0.003841 0.009068 0"
        rpy="0 0 0" />
      <mass
        value="2.673" />
      <inertia
        ixx="0.0033188"
        ixy="7.16E-05"
        ixz="3.77E-07"
        iyy="0.0048743"
        iyz="-4E-09"
        izz="0.0037087" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/RR_hip.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 -0.12 0"
        rpy="1.5707963267948966 0 0" />
      <geometry>
        <cylinder length="0.05" radius="0.07" />
      </geometry>
    </collision>
  </link>
  <joint
    name="RR_hip_joint"
    type="revolute">
    <origin
      xyz="-0.3285 -0.072 0"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="RR_hip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.87"
      upper="0.87"
      effort="200"
      velocity="23" />
  </joint>
  <link name="RR_hip_rotor">
    <!-- <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="RR_hip_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="-0.20205 -0.072 0"/>
    <parent link="base_link"/>
    <child link="RR_hip_rotor"/>
  </joint>
  <link
    name="RR_thigh">
    <inertial>
      <origin
        xyz="-0.006279 0.032049 -0.057835"
        rpy="0 0 0" />
      <mass
        value="4.536" />
      <inertia
        ixx="0.062299"
        ixy="-0.00087781"
        ixz="-0.0036475"
        iyy="0.061399"
        iyz="-0.0083537"
        izz="0.0081997" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/RR_thigh.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.57 0" xyz="-0.03 0 -0.2"/>
      <geometry>
        <box size="0.25 0.028 0.04"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RR_thigh_joint"
    type="revolute">
    <origin
      xyz="0 -0.11973 0"
      rpy="0 0 0" />
    <parent
      link="RR_hip" />
    <child
      link="RR_thigh" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.94"
      upper="4.69"
      effort="200"
      velocity="23" />
  </joint>
  <link name="RR_thigh_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="RR_thigh_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.00798 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh_rotor"/>
  </joint>
  <link
    name="RR_calf">
    <inertial>
      <origin
        xyz="0.012422 0 -0.12499"
        rpy="0 0 0" />
      <mass
        value="0.404" />
      <inertia
        ixx="0.01143"
        ixy="0"
        ixz="0.000643"
        iyy="0.011534"
        iyz="0"
        izz="0.000331" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/RR_calf.dae" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.2 0" xyz="-0.002 0 -0.03"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.35 0" xyz="0.022 0 -0.1"/>
      <geometry>
        <box size="0.1 0.0245 0.018"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 1.53 0" xyz="0.036 0 -0.225"/>
      <geometry>
        <box size="0.15 0.0245 0.015"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 2 0" xyz="0.023 0 -0.31"/>
      <geometry>
        <box size="0.05 0.028 0.035"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="RR_calf_joint"
    type="revolute">
    <origin
      xyz="0 8.6986E-05 -0.35"
      rpy="0 0 0" />
    <parent
      link="RR_thigh" />
    <child
      link="RR_calf" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.82"
      upper="-0.43"
      effort="320"
      velocity="14" />
  </joint>
  <link name="RR_calf_rotor">
    <!-- <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </visual> -->
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.02" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <mass value="0.2734"/>
      <inertia ixx="0.000144463" ixy="0.0" ixz="0.0" iyy="0.000144463" iyz="0.0" izz="0.000263053"/>
    </inertial>
  </link>
  <joint name="RR_calf_rotor_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.05788 0"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf_rotor"/>
  </joint>
  <link
    name="RR_foot">
    <inertial>
      <origin
        xyz="-0.006511 0 -0.010144"
        rpy="0 0 0" />
      <mass
        value="0.126" />
      <inertia
        ixx="3.8E-05"
        ixy="0"
        ixz="1.1E-05"
        iyy="4.1E-05"
        iyz="0"
        izz="4.2E-05" />
    </inertial>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="1.57 0 0" />
      <geometry>
        <sphere radius="0.032" />
      </geometry>
    </collision>
  </link>
  <joint
    name="RR_foot_joint"
    type="fixed">
    <origin
      xyz="0 -8.6984E-05 -0.35"
      rpy="0 0 0" />
    <parent
      link="RR_calf" />
    <child
      link="RR_foot" />
  </joint>
  <link
    name="f_oc_link">
    <inertial>
      <origin
        xyz="-3.39E-15 1.46E-16 -6.11E-16"
        rpy="0 0 0" />
      <mass
        value="4.19E-15" />
      <inertia
        ixx="1.68E-27"
        ixy="-3.89E-44"
        ixz="5.04E-44"
        iyy="1.68E-27"
        iyz="2.81E-45"
        izz="1.68E-27" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/f_oc_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/f_oc_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="joint_f_oc"
    type="fixed">
    <origin
      xyz="0.39079 0 -0.013433"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="base_link" />
    <child
      link="f_oc_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="r_oc_link">
    <inertial>
      <origin
        xyz="-0.39143 1.52E-07 -0.011961"
        rpy="0 0 0" />
      <mass
        value="4.19E-12" />
      <inertia
        ixx="1.68E-22"
        ixy="9.03E-39"
        ixz="0"
        iyy="1.68E-22"
        iyz="-1.2E-38"
        izz="1.68E-22" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/r_oc_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://b2_description/meshes/r_oc_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="joint_r_oc"
    type="fixed">
    <origin
      xyz="-0.38451 0 -0.023249"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="base_link" />
    <child
      link="r_oc_link" />
    <axis
      xyz="0 0 0" />
  </joint>
</robot>
