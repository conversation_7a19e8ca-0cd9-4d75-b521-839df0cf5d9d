<?xml version="1.0"?>

<robot name="go1" xmlns:xacro="http://www.ros.org/wiki/xacro">

	<xacro:arg name="DEBUG" default="false"/>
	<xacro:arg name="GAZEBO" default="false"/>
	<xacro:arg name="CLASSIC" default="false"/>

	<xacro:include filename="$(find go1_description)/xacro/const.xacro"/>
	<xacro:include filename="$(find go1_description)/xacro/materials.xacro"/>
	<xacro:include filename="$(find go1_description)/xacro/leg.xacro"/>

	<xacro:include filename="$(find go1_description)/xacro/depthCamera.xacro"/>
	<xacro:include filename="$(find go1_description)/xacro/ultraSound.xacro"/>

	<xacro:if value="$(arg GAZEBO)">
		<xacro:if value="$(arg CLASSIC)">
			<xacro:include filename="$(find go1_description)/xacro/gazebo_classic.xacro"/>
		</xacro:if>
		<xacro:unless value="$(arg CLASSIC)">
			<xacro:include filename="$(find go1_description)/xacro/gazebo.xacro"/>
		</xacro:unless>
	</xacro:if>
	<xacro:unless value="$(arg GAZEBO)">
		<xacro:include filename="$(find go1_description)/xacro/ros2_control.xacro"/>
	</xacro:unless>


	<!-- <xacro:stairs stairs="15" xpos="0" ypos="0" zpos="0" /> -->

	<!-- Rotor related joint and link is only for demonstrate location. -->
	<!-- Actually, the rotor will rotate and the joint is not fixed. Reduction ratio should be considered. -->

	<!-- Debug mode will hung up the robot, use "true" or "false" to switch it. -->
	<xacro:if value="$(arg DEBUG)">
		<link name="world"/>
		<joint name="base_static_joint" type="fixed">
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<parent link="world"/>
			<child link="base"/>
		</joint>
	</xacro:if>

	<link name="base">
		<visual>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
				<box size="0.001 0.001 0.001"/>
			</geometry>
		</visual>
	</link>

	<joint name="floating_base" type="fixed">
		<origin rpy="0 0 0" xyz="0 0 0"/>
		<parent link="base"/>
		<child link="trunk"/>
	</joint>

	<link name="trunk">
		<visual>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
				<mesh filename="file://$(find go1_description)/meshes/trunk.dae" scale="1 1 1"/>
			</geometry>
			<material name="light-grey"/>
		</visual>
		<collision>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
				<box size="${trunk_length} ${trunk_width} ${trunk_height}"/>
			</geometry>
		</collision>
		<inertial>
			<origin rpy="0 0 0" xyz="${trunk_com_x} ${trunk_com_y} ${trunk_com_z}"/>
			<mass value="${trunk_mass}"/>
			<inertia
							ixx="${trunk_ixx}" ixy="${trunk_ixy}" ixz="${trunk_ixz}"
							iyy="${trunk_iyy}" iyz="${trunk_iyz}"
							izz="${trunk_izz}"/>
		</inertial>
	</link>

	<joint name="imu_joint" type="fixed">
		<parent link="trunk"/>
		<child link="imu_link"/>
		<origin rpy="0 0 0" xyz="-0.01592 -0.06659 -0.00617"/>
	</joint>

	<link name="imu_link">
		<inertial>
			<mass value="0.001"/>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
		</inertial>
		<visual>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
				<box size="0.001 0.001 0.001"/>
			</geometry>
			<!-- <material name="red"/> -->
		</visual>
		<collision>
			<origin rpy="0 0 0" xyz="0 0 0"/>
			<geometry>
				<box size=".001 .001 .001"/>
			</geometry>
		</collision>
	</link>

	<xacro:leg name="FR" mirror="-1" mirror_dae="False" front_hind="1" front_hind_dae="True"/>
	<xacro:leg name="FL" mirror="1" mirror_dae="True" front_hind="1" front_hind_dae="True"/>
	<xacro:leg name="RR" mirror="-1" mirror_dae="False" front_hind="-1" front_hind_dae="False"/>
	<xacro:leg name="RL" mirror="1" mirror_dae="True" front_hind="-1" front_hind_dae="False"/>

	<xacro:depthCamera camID="1" name="face">
		<origin rpy="${PI} 0 0" xyz="0.2785 0.0125 0.0167"/>
	</xacro:depthCamera>

	<xacro:depthCamera camID="2" name="chin">
		<origin rpy="${PI} ${PI/2} 0" xyz="0.2522 0.0125 -0.0436"/>
	</xacro:depthCamera>

	<xacro:depthCamera camID="3" name="left">
		<origin rpy="${PI} 0.2618 ${PI/2}" xyz="-0.066 0.082 -0.0176"/>
	</xacro:depthCamera>

	<xacro:depthCamera camID="4" name="right">
		<origin rpy="${PI} 0.2618 ${-PI/2}" xyz="-0.041 -0.082 -0.0176"/>
	</xacro:depthCamera>

	<xacro:depthCamera camID="5" name="rearDown">
		<origin rpy="${PI} ${PI/2} 0" xyz="-0.0825 0.0125 -0.04365"/>
	</xacro:depthCamera>

	<joint name="camera_laserscan_joint_left" type="fixed">
		<origin rpy="0 0.2618 0" xyz="0 0 0"/>
		<parent link="camera_left"/>
		<child link="camera_laserscan_link_left"/>
	</joint>

	<link name="camera_laserscan_link_left">
	</link>

	<joint name="camera_laserscan_joint_right" type="fixed">
		<origin rpy="0 0.2618 0" xyz="0 0 0"/>
		<parent link="camera_right"/>
		<child link="camera_laserscan_link_right"/>
	</joint>

	<link name="camera_laserscan_link_right">
	</link>

	<xacro:ultraSound name="left">
		<origin rpy="0 0.2618 ${PI/2}" xyz="-0.0535  0.0826 0.00868"/>
	</xacro:ultraSound>

	<xacro:ultraSound name="right">
		<origin rpy="0 0.2618 ${-PI/2}" xyz="-0.0535 -0.0826 0.00868"/>
	</xacro:ultraSound>

	<xacro:ultraSound name="face">
		<origin rpy="0 0 0" xyz="0.2747 0.0 -0.0088"/>
	</xacro:ultraSound>
</robot>
