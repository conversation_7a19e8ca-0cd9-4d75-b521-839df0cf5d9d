<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

	<ros2_control name="UnitreeSystem" type="system">

		<hardware>
			<plugin>hardware_unitree_sdk2/HardwareUnitree</plugin>
		</hardware>

		<joint name="FR_hip_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="FR_thigh_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="FR_calf_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="FL_hip_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="FL_thigh_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="FL_calf_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="RR_hip_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="RR_thigh_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="RR_calf_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="RL_hip_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="RL_thigh_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<joint name="RL_calf_joint">
			<command_interface name="position"/>
			<command_interface name="velocity"/>
			<command_interface name="effort"/>
			<command_interface name="kp"/>
			<command_interface name="kd"/>

			<state_interface name="position"/>
			<state_interface name="velocity"/>
			<state_interface name="effort"/>
		</joint>

		<sensor name="imu_sensor">
			<state_interface name="orientation.w"/>
			<state_interface name="orientation.x"/>
			<state_interface name="orientation.y"/>
			<state_interface name="orientation.z"/>
			<state_interface name="angular_velocity.x"/>
			<state_interface name="angular_velocity.y"/>
			<state_interface name="angular_velocity.z"/>
			<state_interface name="linear_acceleration.x"/>
			<state_interface name="linear_acceleration.y"/>
			<state_interface name="linear_acceleration.z"/>
		</sensor>

		<sensor name="foot_force">
			<state_interface name="FR"/>
			<state_interface name="FL"/>
			<state_interface name="RR"/>
			<state_interface name="RL"/>
		</sensor>

	</ros2_control>

</robot>
