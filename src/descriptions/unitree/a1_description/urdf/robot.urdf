<?xml version="1.0" encoding="utf-8"?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot.xacro                    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="a1">
  <!-- dynamics inertial value -->
  <!-- trunk -->
  <!-- <xacro:property name="trunk_mass" value="5.660"/>
    <xacro:property name="trunk_com_x" value="0.012731"/>
    <xacro:property name="trunk_com_y" value="0.002186"/>
    <xacro:property name="trunk_com_z" value="0.000515"/>
    <xacro:property name="trunk_ixx" value="0.016839930"/>
    <xacro:property name="trunk_ixy" value="0.000083902"/>
    <xacro:property name="trunk_ixz" value="0.000597679"/>
    <xacro:property name="trunk_iyy" value="0.056579028"/>
    <xacro:property name="trunk_iyz" value="0.000025134"/>
    <xacro:property name="trunk_izz" value="0.064713601"/> -->
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.913725490196 0.913725490196 0.847058823529 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.423529411765 0.0392156862745 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.870588235294 0.811764705882 0.764705882353 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <!-- ros_control plugin -->
  <gazebo>
    <plugin filename="libgazebo_ros_control.so" name="gazebo_ros_control">
      <robotNamespace>/a1_gazebo</robotNamespace>
      <robotSimType>gazebo_ros_control/DefaultRobotHWSim</robotSimType>
    </plugin>
  </gazebo>
  <!-- Show the trajectory of trunk center. -->
  <gazebo>
    <plugin filename="libLinkPlot3DPlugin.so" name="3dplotTrunk">
      <frequency>10</frequency>
      <plot>
        <link>base</link>
        <pose>0 0 0 0 0 0</pose>
        <material>Gazebo/Yellow</material>
      </plot>
    </plugin>
  </gazebo>
  <!-- Show the trajectory of foot. You can add another trajectory about another foot. -->
  <gazebo>
    <plugin filename="libLinkPlot3DPlugin.so" name="3dplot">
      <frequency>1000</frequency>
      <plot>
        <link>FR_calf</link>
        <pose>0 0 -0.2 0 0 0</pose>
        <material>Gazebo/Yellow</material>
      </plot>
    </plugin>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_force.so" name="gazebo_ros_force">
      <bodyName>trunk</bodyName>
      <topicName>/apply_force/trunk</topicName>
    </plugin>
  </gazebo>
  <gazebo reference="imu_link">
    <gravity>true</gravity>
    <sensor name="imu_sensor" type="imu">
      <always_on>true</always_on>
      <update_rate>1000</update_rate>
      <visualize>true</visualize>
      <topic>__default_topic__</topic>
      <plugin filename="libgazebo_ros_imu_sensor.so" name="imu_plugin">
        <topicName>trunk_imu</topicName>
        <bodyName>imu_link</bodyName>
        <updateRateHZ>1000.0</updateRateHZ>
        <gaussianNoise>0.0</gaussianNoise>
        <xyzOffset>0 0 0</xyzOffset>
        <rpyOffset>0 0 0</rpyOffset>
        <frameName>imu_link</frameName>
      </plugin>
      <pose>0 0 0 0 0 0</pose>
    </sensor>
  </gazebo>
  <!-- Foot contacts. -->
  <gazebo reference="FR_calf">
    <sensor name="FR_foot_contact" type="contact">
      <update_rate>100</update_rate>
      <plugin filename="libunitreeFootContactPlugin.so" name="contactPlugin"/>
      <contact>
        <collision>FR_calf_fixed_joint_lump__FR_foot_collision_1</collision>
      </contact>
    </sensor>
  </gazebo>
  <gazebo reference="FL_calf">
    <sensor name="FL_foot_contact" type="contact">
      <update_rate>100</update_rate>
      <plugin filename="libunitreeFootContactPlugin.so" name="contactPlugin"/>
      <contact>
        <collision>FL_calf_fixed_joint_lump__FL_foot_collision_1</collision>
      </contact>
    </sensor>
  </gazebo>
  <gazebo reference="RR_calf">
    <sensor name="RR_foot_contact" type="contact">
      <update_rate>100</update_rate>
      <plugin filename="libunitreeFootContactPlugin.so" name="contactPlugin"/>
      <contact>
        <collision>RR_calf_fixed_joint_lump__RR_foot_collision_1</collision>
      </contact>
    </sensor>
  </gazebo>
  <gazebo reference="RL_calf">
    <sensor name="RL_foot_contact" type="contact">
      <update_rate>100</update_rate>
      <plugin filename="libunitreeFootContactPlugin.so" name="contactPlugin"/>
      <contact>
        <collision>RL_calf_fixed_joint_lump__RL_foot_collision_1</collision>
      </contact>
    </sensor>
  </gazebo>
  <!-- Visualization of Foot contacts. -->
  <gazebo reference="FR_foot">
    <visual>
      <plugin filename="libunitreeDrawForcePlugin.so" name="drawForcePlugin">
        <topicName>FR_foot_contact</topicName>
      </plugin>
    </visual>
  </gazebo>
  <gazebo reference="FL_foot">
    <visual>
      <plugin filename="libunitreeDrawForcePlugin.so" name="drawForcePlugin">
        <topicName>FL_foot_contact</topicName>
      </plugin>
    </visual>
  </gazebo>
  <gazebo reference="RR_foot">
    <visual>
      <plugin filename="libunitreeDrawForcePlugin.so" name="drawForcePlugin">
        <topicName>RR_foot_contact</topicName>
      </plugin>
    </visual>
  </gazebo>
  <gazebo reference="RL_foot">
    <visual>
      <plugin filename="libunitreeDrawForcePlugin.so" name="drawForcePlugin">
        <topicName>RL_foot_contact</topicName>
      </plugin>
    </visual>
  </gazebo>
  <gazebo reference="base">
    <material>Gazebo/Green</material>
    <turnGravityOff>false</turnGravityOff>
  </gazebo>
  <gazebo reference="trunk">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="stick_link">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/White</material>
  </gazebo>
  <gazebo reference="imu_link">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/Red</material>
  </gazebo>
  <!-- FL leg -->
  <gazebo reference="FL_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="FL_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="FL_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="FL_foot">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- FR leg -->
  <gazebo reference="FR_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="FR_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="FR_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="FR_foot">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- RL leg -->
  <gazebo reference="RL_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="RL_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="RL_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="RL_foot">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- RR leg -->
  <gazebo reference="RR_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="RR_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="RR_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="RR_foot">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- <xacro:include filename="$(find a1_gazebo)/launch/stairs.urdf.xacro"/> -->
  <!-- <xacro:stairs stairs="15" xpos="0" ypos="0" zpos="0" /> -->
  <!-- Debug mode will hung up the robot, use "true" or "false" to switch it. -->
  <!-- <xacro:if value="$(arg DEBUG)">
        <link name="world"/>
        <joint name="base_static_joint" type="fixed">
            <origin rpy="0 0 0" xyz="0 0 0"/>
            <parent link="world"/>
            <child link="base"/>
        </joint>
    </xacro:if>  -->
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <joint name="floating_base" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base"/>
    <child link="trunk"/>
  </joint>
  <link name="trunk">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/trunk.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.267 0.194 0.114"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0041 -0.0005"/>
      <mass value="6.0"/>
      <inertia ixx="0.0158533" ixy="-3.66e-05" ixz="-6.11e-05" iyy="0.0377999" iyz="-2.75e-05" izz="0.0456542"/>
    </inertial>
  </link>
  <joint name="imu_joint" type="fixed">
    <parent link="trunk"/>
    <child link="imu_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="imu_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size=".001 .001 .001"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.1805 -0.047 0"/>
    <parent link="trunk"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-0.802851455917" upper="0.802851455917" velocity="21"/>
  </joint>
  <link name="FR_hip">
    <visual>
      <origin rpy="3.14159265359 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003311 -0.000635 3.1e-05"/>
      <mass value="0.696"/>
      <inertia ixx="0.000469246" ixy="9.409e-06" ixz="-3.42e-07" iyy="0.00080749" iyz="4.66e-07" izz="0.000552929"/>
    </inertial>
  </link>
  <joint name="FR_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.081 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="FR_thigh_shoulder">
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.041"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.0838 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-1.0471975512" upper="4.18879020479" velocity="21"/>
  </joint>
  <link name="FR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/thigh_mirror.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003237 0.022327 -0.027326"/>
      <mass value="1.013"/>
      <inertia ixx="0.005529065" ixy="-4.825e-06" ixz="0.000343869" iyy="0.005139339" iyz="-2.2448e-05" izz="0.001367788"/>
    </inertial>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-2.69653369433" upper="-0.916297857297" velocity="21"/>
  </joint>
  <link name="FR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.006435 0.0 -0.107388"/>
      <mass value="0.166"/>
      <inertia ixx="0.002997972" ixy="0.0" ixz="-0.000141163" iyy="0.003014022" iyz="0.0" izz="3.2426e-05"/>
    </inertial>
  </link>
  <joint name="FR_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="FR_calf"/>
    <child link="FR_foot"/>
  </joint>
  <link name="FR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.6e-06" ixy="0.0" ixz="0.0" iyy="9.6e-06" iyz="0.0" izz="9.6e-06"/>
    </inertial>
  </link>
  <transmission name="FR_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FR_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FR_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FR_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FR_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FR_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FR_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FR_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FR_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.1805 0.047 0"/>
    <parent link="trunk"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-0.802851455917" upper="0.802851455917" velocity="21"/>
  </joint>
  <link name="FL_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003311 0.000635 3.1e-05"/>
      <mass value="0.696"/>
      <inertia ixx="0.000469246" ixy="-9.409e-06" ixz="-3.42e-07" iyy="0.00080749" iyz="-4.66e-07" izz="0.000552929"/>
    </inertial>
  </link>
  <joint name="FL_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.081 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="FL_thigh_shoulder">
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.041"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0838 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-1.0471975512" upper="4.18879020479" velocity="21"/>
  </joint>
  <link name="FL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/thigh.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003237 -0.022327 -0.027326"/>
      <mass value="1.013"/>
      <inertia ixx="0.005529065" ixy="4.825e-06" ixz="0.000343869" iyy="0.005139339" iyz="2.2448e-05" izz="0.001367788"/>
    </inertial>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-2.69653369433" upper="-0.916297857297" velocity="21"/>
  </joint>
  <link name="FL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.006435 0.0 -0.107388"/>
      <mass value="0.166"/>
      <inertia ixx="0.002997972" ixy="0.0" ixz="-0.000141163" iyy="0.003014022" iyz="0.0" izz="3.2426e-05"/>
    </inertial>
  </link>
  <joint name="FL_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="FL_calf"/>
    <child link="FL_foot"/>
  </joint>
  <link name="FL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.6e-06" ixy="0.0" ixz="0.0" iyy="9.6e-06" iyz="0.0" izz="9.6e-06"/>
    </inertial>
  </link>
  <transmission name="FL_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FL_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FL_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FL_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FL_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FL_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FL_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FL_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FL_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.1805 -0.047 0"/>
    <parent link="trunk"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-0.802851455917" upper="0.802851455917" velocity="21"/>
  </joint>
  <link name="RR_hip">
    <visual>
      <origin rpy="3.14159265359 3.14159265359 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.003311 -0.000635 3.1e-05"/>
      <mass value="0.696"/>
      <inertia ixx="0.000469246" ixy="-9.409e-06" ixz="3.42e-07" iyy="0.00080749" iyz="4.66e-07" izz="0.000552929"/>
    </inertial>
  </link>
  <joint name="RR_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.081 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="RR_thigh_shoulder">
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.041"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.0838 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-1.0471975512" upper="4.18879020479" velocity="21"/>
  </joint>
  <link name="RR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/thigh_mirror.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003237 0.022327 -0.027326"/>
      <mass value="1.013"/>
      <inertia ixx="0.005529065" ixy="-4.825e-06" ixz="0.000343869" iyy="0.005139339" iyz="-2.2448e-05" izz="0.001367788"/>
    </inertial>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-2.69653369433" upper="-0.916297857297" velocity="21"/>
  </joint>
  <link name="RR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.006435 0.0 -0.107388"/>
      <mass value="0.166"/>
      <inertia ixx="0.002997972" ixy="0.0" ixz="-0.000141163" iyy="0.003014022" iyz="0.0" izz="3.2426e-05"/>
    </inertial>
  </link>
  <joint name="RR_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="RR_calf"/>
    <child link="RR_foot"/>
  </joint>
  <link name="RR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.6e-06" ixy="0.0" ixz="0.0" iyy="9.6e-06" iyz="0.0" izz="9.6e-06"/>
    </inertial>
  </link>
  <transmission name="RR_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RR_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RR_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RR_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RR_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RR_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RR_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RR_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RR_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.1805 0.047 0"/>
    <parent link="trunk"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-0.802851455917" upper="0.802851455917" velocity="21"/>
  </joint>
  <link name="RL_hip">
    <visual>
      <origin rpy="0 3.14159265359 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.003311 0.000635 3.1e-05"/>
      <mass value="0.696"/>
      <inertia ixx="0.000469246" ixy="9.409e-06" ixz="3.42e-07" iyy="0.00080749" iyz="-4.66e-07" izz="0.000552929"/>
    </inertial>
  </link>
  <joint name="RL_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.081 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="RL_thigh_shoulder">
    <collision>
      <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.032" radius="0.041"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0838 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-1.0471975512" upper="4.18879020479" velocity="21"/>
  </joint>
  <link name="RL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/thigh.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003237 -0.022327 -0.027326"/>
      <mass value="1.013"/>
      <inertia ixx="0.005529065" ixy="4.825e-06" ixz="0.000343869" iyy="0.005139339" iyz="2.2448e-05" izz="0.001367788"/>
    </inertial>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.01" friction="0.2"/>
    <limit effort="33.5" lower="-2.69653369433" upper="-0.916297857297" velocity="21"/>
  </joint>
  <link name="RL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://a1_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 1.57079632679 0" xyz="0 0 -0.1"/>
      <geometry>
        <box size="0.2 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.006435 0.0 -0.107388"/>
      <mass value="0.166"/>
      <inertia ixx="0.002997972" ixy="0.0" ixz="-0.000141163" iyy="0.003014022" iyz="0.0" izz="3.2426e-05"/>
    </inertial>
  </link>
  <joint name="RL_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="RL_calf"/>
    <child link="RL_foot"/>
  </joint>
  <link name="RL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <material name="orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.6e-06" ixy="0.0" ixz="0.0" iyy="9.6e-06" iyz="0.0" izz="9.6e-06"/>
    </inertial>
  </link>
  <transmission name="RL_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RL_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RL_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RL_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RL_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RL_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RL_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RL_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RL_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <!-- <xacro:stairs stairs="15" xpos="0" ypos="2.0" zpos="0" /> -->
</robot>

