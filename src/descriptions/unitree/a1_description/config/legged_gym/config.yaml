model_name: "rl_sar.pt"
framework: "isaacgym"
rows: 4
cols: 3
decimation: 4
num_observations: 45
observations: ["ang_vel", "gravity_vec", "commands", "dof_pos", "dof_vel", "actions"]
observations_history: [0, 1, 2, 3, 4, 5]
clip_obs: 100.0
clip_actions_lower: [-100, -100, -100,
                     -100, -100, -100,
                     -100, -100, -100,
                     -100, -100, -100]
clip_actions_upper: [100, 100, 100,
                     100, 100, 100,
                     100, 100, 100,
                     100, 100, 100]
rl_kp: [30, 30, 30,
        30, 30, 30,
        30, 30, 30,
        30, 30, 30]
rl_kd: [0.754, 0.754, 0.754,
        0.754, 0.754, 0.754,
        0.754, 0.754, 0.754,
        0.754, 0.754, 0.754]
hip_scale_reduction: 0.5
hip_scale_reduction_indices: [0, 3, 6, 9]
num_of_dofs: 12
action_scale: 0.25
lin_vel_scale: 2.0
ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
commands_scale: [2.0, 2.0, 1.0]
torque_limits: [33.5, 33.5, 33.5,
                33.5, 33.5, 33.5,
                33.5, 33.5, 33.5,
                33.5, 33.5, 33.5]
