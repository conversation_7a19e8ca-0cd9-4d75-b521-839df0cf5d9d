<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot.xacro                    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="go2">
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0.2 0.2 0.2 0.0"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.4235294117647059 0.0392156862745098 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.8705882352941177 0.8117647058823529 0.7647058823529411 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <ros2_control name="UnitreeSystem" type="system">
    <hardware>
      <plugin>hardware_unitree_sdk2/HardwareUnitree</plugin>
    </hardware>
    <joint name="FR_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="FR_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="FR_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="FL_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="FL_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="FL_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="RR_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="RR_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="RR_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="RL_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="RL_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="RL_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <sensor name="imu_sensor">
      <state_interface name="orientation.w"/>
      <state_interface name="orientation.x"/>
      <state_interface name="orientation.y"/>
      <state_interface name="orientation.z"/>
      <state_interface name="angular_velocity.x"/>
      <state_interface name="angular_velocity.y"/>
      <state_interface name="angular_velocity.z"/>
      <state_interface name="linear_acceleration.x"/>
      <state_interface name="linear_acceleration.y"/>
      <state_interface name="linear_acceleration.z"/>
    </sensor>
    <sensor name="foot_force">
      <state_interface name="FR"/>
      <state_interface name="FL"/>
      <state_interface name="RR"/>
      <state_interface name="RL"/>
    </sensor>
  </ros2_control>
  <!-- <xacro:stairs stairs="15" xpos="0" ypos="0" zpos="0" /> -->
  <!-- Rotor related joint and link is only for demonstrate location. -->
  <!-- Actually, the rotor will rotate and the joint is not fixed. Reduction ratio should be considered. -->
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <joint name="floating_base" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base"/>
    <child link="trunk"/>
  </joint>
  <link name="trunk">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/trunk.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.3762 0.0935 0.114"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.021112 0.0 -0.005366"/>
      <mass value="6.921"/>
      <inertia ixx="0.02448" ixy="0.00012166" ixz="0.0014849" iyy="0.098077" iyz="-3.12e-05" izz="0.107"/>
    </inertial>
  </link>
  <joint name="imu_joint" type="fixed">
    <parent link="trunk"/>
    <child link="imu_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="imu_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
      <!-- <material name="red"/> -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size=".001 .001 .001"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.1934 -0.0465 0"/>
    <parent link="trunk"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.0472" upper="1.0472" velocity="30.1"/>
  </joint>
  <link name="FR_hip">
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 -0.0955 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0054 -0.00194 -0.000105"/>
      <mass value="0.678"/>
      <inertia ixx="0.00048" ixy="3.01e-06" ixz="1.11e-06" iyy="0.000884" iyz="1.42e-06" izz="0.000596"/>
    </inertial>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.0955 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.5708" upper="3.4907" velocity="30.1"/>
  </joint>
  <link name="FR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/thigh_mirror.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00374 0.0223 -0.0327"/>
      <mass value="1.152"/>
      <inertia ixx="0.00584" ixy="-8.72e-05" ixz="-0.000289" iyy="0.0058" iyz="-0.000808" izz="0.00103"/>
    </inertial>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="35.55" lower="-2.7227" upper="-0.83776" velocity="20.06"/>
  </joint>
  <link name="FR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00548 -0.000975 -0.115"/>
      <mass value="0.154"/>
      <inertia ixx="0.00108" ixy="3.4e-07" ixz="1.72e-05" iyy="0.0011" iyz="8.28e-06" izz="3.29e-05"/>
    </inertial>
  </link>
  <joint name="FR_foot_fixed" type="fixed">
    0
            
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="FR_calf"/>
    <child link="FR_foot"/>
  </joint>
  <link name="FR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.600000000000001e-06" ixy="0.0" ixz="0.0" iyy="9.600000000000001e-06" iyz="0.0" izz="9.600000000000001e-06"/>
    </inertial>
  </link>
  <transmission name="FR_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FR_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FR_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FR_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FR_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FR_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FR_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FR_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FR_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.1934 0.0465 0"/>
    <parent link="trunk"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.0472" upper="1.0472" velocity="30.1"/>
  </joint>
  <link name="FL_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0.0955 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0054 0.00194 -0.000105"/>
      <mass value="0.678"/>
      <inertia ixx="0.00048" ixy="-3.01e-06" ixz="1.11e-06" iyy="0.000884" iyz="-1.42e-06" izz="0.000596"/>
    </inertial>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0955 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.5708" upper="3.4907" velocity="30.1"/>
  </joint>
  <link name="FL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/thigh.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00374 -0.0223 -0.0327"/>
      <mass value="1.152"/>
      <inertia ixx="0.00584" ixy="8.72e-05" ixz="-0.000289" iyy="0.0058" iyz="0.000808" izz="0.00103"/>
    </inertial>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="35.55" lower="-2.7227" upper="-0.83776" velocity="20.06"/>
  </joint>
  <link name="FL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00548 -0.000975 -0.115"/>
      <mass value="0.154"/>
      <inertia ixx="0.00108" ixy="3.4e-07" ixz="1.72e-05" iyy="0.0011" iyz="8.28e-06" izz="3.29e-05"/>
    </inertial>
  </link>
  <joint name="FL_foot_fixed" type="fixed">
    0
            
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="FL_calf"/>
    <child link="FL_foot"/>
  </joint>
  <link name="FL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.600000000000001e-06" ixy="0.0" ixz="0.0" iyy="9.600000000000001e-06" iyz="0.0" izz="9.600000000000001e-06"/>
    </inertial>
  </link>
  <transmission name="FL_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FL_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FL_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FL_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FL_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FL_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="FL_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="FL_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="FL_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.1934 -0.0465 0"/>
    <parent link="trunk"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.0472" upper="1.0472" velocity="30.1"/>
  </joint>
  <link name="RR_hip">
    <visual>
      <origin rpy="3.141592653589793 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 -0.0955 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0054 -0.00194 -0.000105"/>
      <mass value="0.678"/>
      <inertia ixx="0.00048" ixy="-3.01e-06" ixz="-1.11e-06" iyy="0.000884" iyz="1.42e-06" izz="0.000596"/>
    </inertial>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.0955 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.5708" upper="3.4907" velocity="30.1"/>
  </joint>
  <link name="RR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/thigh_mirror.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00374 0.0223 -0.0327"/>
      <mass value="1.152"/>
      <inertia ixx="0.00584" ixy="-8.72e-05" ixz="-0.000289" iyy="0.0058" iyz="-0.000808" izz="0.00103"/>
    </inertial>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="35.55" lower="-2.7227" upper="-0.83776" velocity="20.06"/>
  </joint>
  <link name="RR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00548 -0.000975 -0.115"/>
      <mass value="0.154"/>
      <inertia ixx="0.00108" ixy="3.4e-07" ixz="1.72e-05" iyy="0.0011" iyz="8.28e-06" izz="3.29e-05"/>
    </inertial>
  </link>
  <joint name="RR_foot_fixed" type="fixed">
    0
            
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="RR_calf"/>
    <child link="RR_foot"/>
  </joint>
  <link name="RR_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.600000000000001e-06" ixy="0.0" ixz="0.0" iyy="9.600000000000001e-06" iyz="0.0" izz="9.600000000000001e-06"/>
    </inertial>
  </link>
  <transmission name="RR_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RR_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RR_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RR_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RR_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RR_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RR_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RR_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RR_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.1934 0.0465 0"/>
    <parent link="trunk"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.0472" upper="1.0472" velocity="30.1"/>
  </joint>
  <link name="RL_hip">
    <visual>
      <origin rpy="0 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/hip.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0.0955 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0054 0.00194 -0.000105"/>
      <mass value="0.678"/>
      <inertia ixx="0.00048" ixy="3.01e-06" ixz="-1.11e-06" iyy="0.000884" iyz="-1.42e-06" izz="0.000596"/>
    </inertial>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0955 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="23.7" lower="-1.5708" upper="3.4907" velocity="30.1"/>
  </joint>
  <link name="RL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/thigh.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.0245 0.034"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00374 -0.0223 -0.0327"/>
      <mass value="1.152"/>
      <inertia ixx="0.00584" ixy="8.72e-05" ixz="-0.000289" iyy="0.0058" iyz="0.000808" izz="0.00103"/>
    </inertial>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="35.55" lower="-2.7227" upper="-0.83776" velocity="20.06"/>
  </joint>
  <link name="RL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file:///home/<USER>/ros2_ws/install/go2_description/share/go2_description/meshes/calf.dae" scale="1 1 1"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.1065"/>
      <geometry>
        <box size="0.213 0.016 0.016"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00548 -0.000975 -0.115"/>
      <mass value="0.154"/>
      <inertia ixx="0.00108" ixy="3.4e-07" ixz="1.72e-05" iyy="0.0011" iyz="8.28e-06" izz="3.29e-05"/>
    </inertial>
  </link>
  <joint name="RL_foot_fixed" type="fixed">
    0
            
    <origin rpy="0 0 0" xyz="0 0 -0.213"/>
    <parent link="RL_calf"/>
    <child link="RL_foot"/>
  </joint>
  <link name="RL_foot">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.01"/>
      </geometry>
      <!-- <material name="orange"/> -->
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.06"/>
      <inertia ixx="9.600000000000001e-06" ixy="0.0" ixz="0.0" iyy="9.600000000000001e-06" iyz="0.0" izz="9.600000000000001e-06"/>
    </inertial>
  </link>
  <transmission name="RL_hip_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RL_hip_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RL_hip_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RL_thigh_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RL_thigh_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RL_thigh_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RL_calf_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RL_calf_joint">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RL_calf_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
</robot>
