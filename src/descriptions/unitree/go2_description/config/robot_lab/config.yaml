model_name: "policy.pt"
framework: "isaacsim"
decimation: 4
num_observations: 45
observations: [ "ang_vel", "gravity_vec", "commands", "dof_pos", "dof_vel", "actions" ]
#observations_history: [ ]  # 0 is the latest observation
clip_obs: 100.0
clip_actions_lower: [ -100.0, -100.0, -100.0,
                      -100.0, -100.0, -100.0,
                      -100.0, -100.0, -100.0,
                      -100.0, -100.0, -100.0 ]
clip_actions_upper: [ 100.0, 100.0, 100.0,
                      100.0, 100.0, 100.0,
                      100.0, 100.0, 100.0,
                      100.0, 100.0, 100.0 ]
rl_kp: [ 20.0, 20.0, 20.0,
         20.0, 20.0, 20.0,
         20.0, 20.0, 20.0,
         20.0, 20.0, 20.0 ]
rl_kd: [ 0.5, 0.5, 0.5,
         0.5, 0.5, 0.5,
         0.5, 0.5, 0.5,
         0.5, 0.5, 0.5 ]
hip_scale_reduction: 1.0
hip_scale_reduction_indices: [ 0, 3, 6, 9 ]
num_of_dofs: 12
action_scale: 0.25

lin_vel_scale: 2.0
ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
commands_scale: [ 1.0, 1.0, 1.0 ]

torque_limits: [ 23.5, 23.5, 23.5,
                 23.5, 23.5, 23.5,
                 23.5, 23.5, 23.5,
                 23.5, 23.5, 23.5 ]
