<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
  <xacro:include filename="$(find gz_quadruped_hardware)/xacro/foot_force_sensor.xacro"/>
  <ros2_control name="GazeboSystem" type="system">
    <hardware>
      <plugin>gz_quadruped_hardware/GazeboSimSystem</plugin>
    </hardware>

    <joint name="FR_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>

      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="FR_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="FR_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="FL_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="FL_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="FL_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="RR_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="RR_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="RR_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="RL_hip_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="RL_thigh_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <joint name="RL_calf_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <command_interface name="effort"/>
      <command_interface name="kp"/>
      <command_interface name="kd"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>

    <sensor name="imu_sensor">
      <state_interface name="orientation.x"/>
      <state_interface name="orientation.y"/>
      <state_interface name="orientation.z"/>
      <state_interface name="orientation.w"/>
      <state_interface name="angular_velocity.x"/>
      <state_interface name="angular_velocity.y"/>
      <state_interface name="angular_velocity.z"/>
      <state_interface name="linear_acceleration.x"/>
      <state_interface name="linear_acceleration.y"/>
      <state_interface name="linear_acceleration.z"/>
    </sensor>

    <sensor name="foot_force">
      <state_interface name="FR_foot_force"/>
      <state_interface name="FL_foot_force"/>
      <state_interface name="RR_foot_force"/>
      <state_interface name="RL_foot_force"/>
    </sensor>

  </ros2_control>

  <gazebo>
    <plugin filename="gz_quadruped_hardware-system" name="gz_quadruped_hardware::GazeboSimQuadrupedPlugin">
      <parameters>$(find go2_description)/config/gazebo.yaml</parameters>
    </plugin>
    <plugin filename="gz-sim-imu-system" name="gz::sim::systems::Imu"/>
    <plugin filename="gz-sim-forcetorque-system" name="gz::sim::systems::ForceTorque"/>
  </gazebo>

  <gazebo reference="trunk">
    <mu1>0.6</mu1>
    <mu2>0.6</mu2>
    <self_collide>1</self_collide>
  </gazebo>

  <gazebo reference="imu_link">
    <sensor name="imu_sensor" type="imu">
      <always_on>1</always_on>
      <update_rate>1000</update_rate>
      <visualize>true</visualize>
      <topic>imu</topic>
      <imu>
        <angular_velocity>
          <x>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-4</stddev>
              <bias_mean>0.0000075</bias_mean>
              <bias_stddev>0.0000008</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-4</stddev>
              <bias_mean>0.0000075</bias_mean>
              <bias_stddev>0.0000008</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>2e-4</stddev>
              <bias_mean>0.0000075</bias_mean>
              <bias_stddev>0.0000008</bias_stddev>
            </noise>
          </z>
        </angular_velocity>
        <linear_acceleration>
          <x>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>1.7e-2</stddev>
              <bias_mean>0.1</bias_mean>
              <bias_stddev>0.001</bias_stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>1.7e-2</stddev>
              <bias_mean>0.1</bias_mean>
              <bias_stddev>0.001</bias_stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <mean>0.0</mean>
              <stddev>1.7e-2</stddev>
              <bias_mean>0.1</bias_mean>
              <bias_stddev>0.001</bias_stddev>
            </noise>
          </z>
        </linear_acceleration>
      </imu>
    </sensor>
  </gazebo>

  <gazebo reference="imu_joint">
    <disableFixedJointLumping>true</disableFixedJointLumping>
  </gazebo>

  <xacro:foot_force_sensor name="FL" suffix="foot_fixed"/>
  <xacro:foot_force_sensor name="RL" suffix="foot_fixed"/>
  <xacro:foot_force_sensor name="FR" suffix="foot_fixed"/>
  <xacro:foot_force_sensor name="RR" suffix="foot_fixed"/>

  <xacro:arg name="EXTERNAL_SENSORS" default="false"/>
  <xacro:if value="$(arg EXTERNAL_SENSORS)">
    <xacro:include filename="$(find gz_quadruped_playground)/models/D435/model.xacro"/>

    <xacro:d435 camID="1" name="d435">
      <origin rpy="0 0.1 0" xyz="0.28 0 0.093"/>
    </xacro:d435>

    <gazebo reference="front_camera">
      <sensor name="front_camera" type="camera">
        <camera>
          <horizontal_fov>2.094</horizontal_fov>
          <image>
            <width>1280</width>
            <height>720</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>15</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <!-- Noise is sampled independently per pixel on each frame.
                That pixel's noise value is added to each of its color
                channels, which at that point lie in the range [0,1]. -->
            <mean>0.0</mean>
            <stddev>0.007</stddev>
          </noise>
          <optical_frame_id>front_camera</optical_frame_id>
          <camera_info_topic>camera/camera_info</camera_info_topic>
        </camera>
        <always_on>true</always_on>
        <update_rate>15</update_rate>
        <visualize>true</visualize>
        <topic>camera/image</topic>
      </sensor>
    </gazebo>
    <gazebo reference="lidar">
      <sensor name='L1_lidar' type='gpu_lidar'>
        <topic>scan</topic>
        <update_rate>10</update_rate>
        <gz_frame_id>lidar</gz_frame_id>
        <lidar>
          <scan>
            <horizontal>
              <samples>640</samples>
              <resolution>1</resolution>
              <min_angle>1.396263</min_angle>
              <max_angle>4.8869</max_angle>
            </horizontal>
            <vertical>
              <samples>16</samples>
              <resolution>1</resolution>
              <min_angle>-0.261799</min_angle>
              <max_angle>0.261799</max_angle>
            </vertical>
          </scan>
          <range>
            <min>0.05</min>
            <max>10.0</max>
            <resolution>0.01</resolution>
          </range>
        </lidar>
        <always_on>true</always_on>
        <visualize>true</visualize>
      </sensor>
    </gazebo>
  </xacro:if>
</robot>
