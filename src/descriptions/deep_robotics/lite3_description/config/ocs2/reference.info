targetDisplacementVelocity          0.5;
targetRotationVelocity              1.57;

comHeight                           0.3;

defaultJointState
{
   (0,0)   0.1    ; FL_HipX
   (1,0)   -0.985 ; FL_HipY
   (2,0)   2.084  ; FL_Knee
   (3,0)   -0.1   ; FR_HipX
   (4,0)   -0.985 ; FR_HipY
   (5,0)   2.084  ; FR_Knee
   (6,0)   0.1    ; HL_HipX
   (7,0)   -0.985 ; HL_HipY
   (8,0)   2.084  ; HL_<PERSON>nee
   (9,0)   -0.1   ; HR_HipX
   (10,0)  -0.985 ; HR_HipY
   (11,0)  2.084  ; HR_Knee
}

initialModeSchedule
{
  modeSequence
  {
    [0]  STANCE
    [1]  STANCE
  }
  eventTimes
  {
    [0]  0.5
  }
}

defaultModeSequenceTemplate
{
  modeSequence
  {
    [0]  STANCE
  }
  switchingTimes
  {
    [0]  0.0
    [1]  1.0
  }
}
