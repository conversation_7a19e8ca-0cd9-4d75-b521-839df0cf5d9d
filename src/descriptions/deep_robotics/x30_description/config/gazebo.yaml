# Controller Manager configuration
controller_manager:
  ros__parameters:
    update_rate: 1000  # Hz

    # Define the available controllers
    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

    imu_sensor_broadcaster:
      type: imu_sensor_broadcaster/IMUSensorBroadcaster

    leg_pd_controller:
      type: leg_pd_controller/LegPdController

    unitree_guide_controller:
      type: unitree_guide_controller/UnitreeGuideController

    rl_quadruped_controller:
      type: rl_quadruped_controller/LeggedGymController

imu_sensor_broadcaster:
  ros__parameters:
    sensor_name: "imu_sensor"
    frame_id: "imu_link"

leg_pd_controller:
  ros__parameters:
    update_rate: 1000  # Hz
    joints:
      - FR_HipX
      - FR_HipY
      - FR_Knee
      - FL_HipX
      - FL_HipY
      - FL_Knee
      - HR_HipX
      - HR_HipY
      - HR_Knee
      - HL_HipX
      - HL_HipY
      - HL_Knee

    command_interfaces:
      - effort

    state_interfaces:
      - position
      - velocity

unitree_guide_controller:
  ros__parameters:
    update_rate: 200  # Hz
    command_prefix: "leg_pd_controller"
    joints:
      - FR_HipX
      - FR_HipY
      - FR_Knee
      - FL_HipX
      - FL_HipY
      - FL_Knee
      - HR_HipX
      - HR_HipY
      - HR_Knee
      - HL_HipX
      - HL_HipY
      - HL_Knee

    stand_kp: 300.0
    stand_kd: 10.0

    down_pos:
      - 0.0
      - -1.22
      - 2.61
      - 0.0
      - -1.22
      - 2.61
      - 0.0
      - -1.22
      - 2.61
      - 0.0
      - -1.22
      - 2.61

    stand_pos:
      - 0.0
      - -0.732
      - 1.361
      - 0.0
      - -0.732
      - 1.361
      - 0.0
      - -0.732
      - 1.361
      - 0.0
      - -0.732
      - 1.361

    command_interfaces:
      - effort
      - position
      - velocity
      - kp
      - kd

    state_interfaces:
      - effort
      - position
      - velocity

    feet_names:
      - FR_FOOT
      - FL_FOOT
      - HR_FOOT
      - HL_FOOT

    imu_name: "imu_sensor"
    base_name: "base"

    imu_interfaces:
      - orientation.w
      - orientation.x
      - orientation.y
      - orientation.z
      - angular_velocity.x
      - angular_velocity.y
      - angular_velocity.z
      - linear_acceleration.x
      - linear_acceleration.y
      - linear_acceleration.z

rl_quadruped_controller:
  ros__parameters:
    update_rate: 200  # Hz
    config_folder: "/home/<USER>/ros2_ws/install/a1_description/share/a1_description/config/legged_gym"
    command_prefix: "leg_pd_controller"
    joints:
      - FL_hip_joint
      - FL_thigh_joint
      - FL_calf_joint
      - FR_hip_joint
      - FR_thigh_joint
      - FR_calf_joint
      - RL_hip_joint
      - RL_thigh_joint
      - RL_calf_joint
      - RR_hip_joint
      - RR_thigh_joint
      - RR_calf_joint

    command_interfaces:
      - effort
      - position
      - velocity
      - kp
      - kd

    state_interfaces:
      - effort
      - position
      - velocity

    imu_name: "imu_sensor"
    imu_interfaces:
      - orientation.w
      - orientation.x
      - orientation.y
      - orientation.z
      - angular_velocity.x
      - angular_velocity.y
      - angular_velocity.z
      - linear_acceleration.x
      - linear_acceleration.y
      - linear_acceleration.z