targetDisplacementVelocity          1.0;
targetRotationVelocity              1.57;

comHeight                           0.51;

defaultJointState
{
   (0,0)   0.0    ; FL_HipX
   (1,0)   -0.732 ; FL_HipY
   (2,0)   1.361  ; FL_Knee
   (3,0)   -0.0   ; FR_HipX
   (4,0)   -0.732 ; FR_HipY
   (5,0)   1.361  ; FR_Knee
   (6,0)   0.0    ; HL_HipX
   (7,0)   -0.732 ; HL_HipY
   (8,0)   1.361  ; H<PERSON>_<PERSON>nee
   (9,0)   -0.0   ; HR_HipX
   (10,0)  -0.732 ; HR_HipY
   (11,0)  1.361  ; HR_<PERSON>nee
}

initialModeSchedule
{
  modeSequence
  {
    [0]  STANCE
    [1]  STANCE
  }
  eventTimes
  {
    [0]  0.5
  }
}

defaultModeSequenceTemplate
{
  modeSequence
  {
    [0]  STANCE
  }
  switchingTimes
  {
    [0]  0.0
    [1]  1.0
  }
}
