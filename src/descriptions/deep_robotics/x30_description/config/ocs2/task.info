centroidalModelType             0      // 0: FullCentroidalDynamics, 1: Single Rigid Body Dynamics

legged_robot_interface
{
  verbose                               false  // show the loaded parameters
}

model_settings
{
  positionErrorGain             0.0
  phaseTransitionStanceTime     0.1

  verboseCppAd                  true
  recompileLibrariesCppAd       false
  modelFolderCppAd              ocs2_cpp_ad/x30
}

swing_trajectory_config
{
  liftOffVelocity               0.05
  touchDownVelocity            -0.1
  swingHeight                   0.14
  swingTimeScale                0.15
}

; Multiple_Shooting SQP settings
sqp
{
  nThreads                              3
  dt                                    0.015
  sqpIteration                          1
  deltaTol                              1e-4
  g_max                                 1e-2
  g_min                                 1e-6
  inequalityConstraintMu                0.1
  inequalityConstraintDelta             5.0
  projectStateInputEqualityConstraints  true
  printSolverStatistics                 true
  printSolverStatus                     false
  printLinesearch                       false
  useFeedbackPolicy                     false
  integratorType                        RK2
  threadPriority                        50
}

; Rollout settings
rollout
{
  AbsTolODE                       1e-5
  RelTolODE                       1e-3
  timeStep                        0.015
  integratorType                  ODE45
  maxNumStepsPerSecond            10000
  checkNumericalStability         false
}

mpc
{
  timeHorizon                     1.0  ; [s]
  solutionTimeWindow              -1   ; maximum [s]
  coldStart                       false

  debugPrint                      false

  mpcDesiredFrequency             100  ; [Hz]
  mrtDesiredFrequency             1000 ; [Hz] Useless
}

initialState
{
   ;; Normalized Centroidal Momentum: [linear, angular] ;;
   (0,0)  0.0     ; vcom_x
   (1,0)  0.0     ; vcom_y
   (2,0)  0.0     ; vcom_z
   (3,0)  0.0     ; L_x / robotMass
   (4,0)  0.0     ; L_y / robotMass
   (5,0)  0.0     ; L_z / robotMass

   ;; Base Pose: [position, orientation] ;;
   (6,0)  0.0     ; p_base_x
   (7,0)  0.0     ; p_base_y
   (8,0)  0.51    ; p_base_z
   (9,0)  0.0     ; theta_base_z
   (10,0) 0.0     ; theta_base_y
   (11,0) 0.0     ; theta_base_x

   ;; Leg Joint Positions: [FL, HL, FR, HR] ;;
   (12,0)   0.0    ; FL_HipX
   (13,0)   -0.732 ; FL_HipY
   (14,0)   1.361  ; FL_Knee
   (15,0)   0.0    ; HL_HipX
   (16,0)   -0.732 ; HL_HipY
   (17,0)   1.361  ; HL_Knee
   (18,0)   -0.0   ; FR_HipX
   (19,0)   -0.732 ; FR_HipY
   (20,0)   1.361  ; FR_Knee
   (21,0)   -0.0   ; HR_HipX
   (22,0)   -0.732 ; HR_HipY
   (23,0)   1.361  ; HR_Knee
}

; standard state weight matrix
Q
{
  scaling 1e+0

  ;; Normalized Centroidal Momentum: [linear, angular] ;;
  (0,0)   15.0     ; vcom_x
  (1,1)   15.0     ; vcom_y
  (2,2)   100.0    ; vcom_z
  (3,3)   10.0     ; L_x / robotMass
  (4,4)   30.0     ; L_y / robotMass
  (5,5)   30.0     ; L_z / robotMass

  ;; Base Pose: [position, orientation] ;;
  (6,6)   1000.0   ; p_base_x
  (7,7)   1000.0   ; p_base_y
  (8,8)   1500.0   ; p_base_z
  (9,9)   100.0    ; theta_base_z
  (10,10) 300.0    ; theta_base_y
  (11,11) 300.0    ; theta_base_x

  ;; Leg Joint Positions: [FL, HL, FR, HR] ;;
  (12,12) 5.0     ; FL_HipX
  (13,13) 5.0     ; FL_HipY
  (14,14) 2.5     ; FL_Knee
  (15,15) 5.0     ; HL_HipX
  (16,16) 5.0     ; HL_HipY
  (17,17) 2.5     ; HL_Knee
  (18,18) 5.0     ; FR_HipX
  (19,19) 5.0     ; FR_HipY
  (20,20) 2.5     ; HR_Knee
  (21,21) 5.0     ; HR_HipX
  (22,22) 5.0     ; HR_HipY
  (23,23) 2.5     ; HR_Knee
}

; control weight matrix
R
{
  scaling 1e-3

  ;; Feet Contact Forces: [FL, FR, HL, HR] ;;
  (0,0)   1.0       ; front_left_force
  (1,1)   1.0       ; front_left_force
  (2,2)   1.0       ; front_left_force
  (3,3)   1.0       ; front_right_force
  (4,4)   1.0       ; front_right_force
  (5,5)   1.0       ; front_right_force
  (6,6)   1.0       ; rear_left_force
  (7,7)   1.0       ; rear_left_force
  (8,8)   1.0       ; rear_left_force
  (9,9)   1.0       ; rear_right_force
  (10,10) 1.0       ; rear_right_force
  (11,11) 1.0       ; rear_right_force

  ;; foot velocity relative to base: [FL, HL, FR, HR] (uses the Jacobian at nominal configuration) ;;
  (12,12) 5000.0    ; x
  (13,13) 5000.0    ; y
  (14,14) 5000.0    ; z
  (15,15) 5000.0    ; x
  (16,16) 5000.0    ; y
  (17,17) 5000.0    ; z
  (18,18) 5000.0    ; x
  (19,19) 5000.0    ; y
  (20,20) 5000.0    ; z
  (21,21) 5000.0    ; x
  (22,22) 5000.0    ; y
  (23,23) 5000.0    ; z
}

frictionConeSoftConstraint
{
  frictionCoefficient    0.3

  ; relaxed log barrier parameters
  mu                     0.1
  delta                  5.0
}

selfCollision
{
  ; Self Collision raw object pairs
  collisionObjectPairs
  {
  }

  ; Self Collision pairs
  collisionLinkPairs
  {
    [0] "FL_SHANK, FR_SHANK"
    [1] "HL_SHANK, HR_SHANK"
    [2] "FL_SHANK, HL_SHANK"
    [3] "FR_SHANK, HR_SHANK"
    [4] "FL_FOOT, FR_FOOT"
    [5] "HL_FOOT, HR_FOOT"
    [6] "FL_FOOT, HL_FOOT"
    [7] "FR_FOOT, HR_FOOT"
  }

  minimumDistance  0.05

  ; relaxed log barrier parameters
  mu      1e-2
  delta   1e-3
}

; Whole body control
torqueLimitsTask
{
   (0,0)  84.0     ; Hip_X
   (1,0)  84.0     ; Hip_Y
   (2,0)  160.0    ; Knee
}

frictionConeTask
{
  frictionCoefficient    0.5
}

swingLegTask
{
    kp                   500
    kd                   100
}

weight
{
    swingLeg        100
    baseAccel       1
    contactForce    0.01
}

; State Estimation
kalmanFilter
{
    footRadius                  0.0165
    imuProcessNoisePosition     0.02
    imuProcessNoiseVelocity     0.02
    footProcessNoisePosition    0.002
    footSensorNoisePosition     0.005
    footSensorNoiseVelocity     0.1
    footHeightSensorNoise       0.01
}
