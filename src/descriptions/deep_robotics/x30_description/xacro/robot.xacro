<?xml version="1.0"?>

<robot name="x30" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <xacro:include filename="$(find x30_description)/xacro/body.xacro"/>
  <xacro:include filename="$(find x30_description)/xacro/materials.xacro"/>
  <xacro:include filename="$(find x30_description)/xacro/transmission.xacro"/>

  <xacro:arg name="GAZEBO" default="false"/>
  <xacro:arg name="CLASSIC" default="false"/>


  <xacro:if value="$(arg GAZEBO)">
    <xacro:if value="$(arg CLASSIC)">
      <xacro:include filename="$(find x30_description)/xacro/gazebo_classic.xacro"/>
    </xacro:if>
    <xacro:unless value="$(arg CLASSIC)">
      <xacro:include filename="$(find x30_description)/xacro/gazebo.xacro"/>
    </xacro:unless>
  </xacro:if>
  <xacro:unless value="$(arg GAZEBO)">
    <xacro:include filename="$(find x30_description)/xacro/ros2_control.xacro"/>
  </xacro:unless>

</robot>
