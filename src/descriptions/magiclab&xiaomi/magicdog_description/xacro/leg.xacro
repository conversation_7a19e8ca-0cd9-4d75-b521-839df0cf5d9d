<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
  <xacro:macro name="magicdog_leg" params="name side front_hind hip_xyz">
    <!-- Hip joint -->
    <joint name="${name}_hip_joint" type="revolute">
      <origin xyz="${hip_xyz}" rpy="0 0 0"/>
      <parent link="base"/>
      <child link="${name}_hip"/>
      <axis xyz="1 0 0"/>
      <dynamics damping="0.01" friction="0.1"/>
      <limit effort="25" lower="-0.7" upper="0.8" velocity="22"/>
    </joint>
    <link name="${name}_hip">
      <visual>
        <origin rpy="0 0 0" xyz="0 0 0"/>
        <geometry>
          <xacro:if value="${side == 'R'}">
            <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_hip_R.STL" scale="1 1 1"/>
          </xacro:if>
          <xacro:if value="${side == 'L'}">
            <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_hip_L.STL" scale="1 1 1"/>
          </xacro:if>
        </geometry>
        <material name="grey"/>
      </visual>
      <collision>
        <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
        <geometry>
          <cylinder length="0.041" radius="0.05"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="-0.034672887 0.0039130227 0.0"/>
        <mass value="0.35996359"/>
        <inertia ixx="0.00010289238" ixy="-3.0677028e-05" ixz="0" iyy="0.00047667644" iyz="0.0" izz="0.00046973335"/>
      </inertial>
    </link>
    <joint name="${name}_hip_fixed" type="fixed">
      <xacro:if value="${side == 'R'}">
        <origin rpy="0 0 0" xyz="0 -0.075 0"/>
      </xacro:if>
      <xacro:if value="${side == 'L'}">
        <origin rpy="0 0 0" xyz="0 0.075 0"/>
      </xacro:if>
      <parent link="${name}_hip"/>
      <child link="${name}_thigh_shoulder"/>
    </joint>
    <link name="${name}_thigh_shoulder">
      <collision>
        <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
        <geometry>
          <cylinder length="0.07" radius="0.05"/>
        </geometry>
      </collision>
    </link>
    <joint name="${name}_thigh_joint" type="revolute">
      <xacro:if value="${side == 'R'}">
        <origin rpy="0 0 0" xyz="0 -0.099 0"/>
      </xacro:if>
      <xacro:if value="${side == 'L'}">
        <origin rpy="0 0 0" xyz="0 0.099 0"/>
      </xacro:if>
      <parent link="${name}_hip"/>
      <child link="${name}_thigh"/>
      <axis xyz="0 1 0"/>
      <dynamics damping="0.01" friction="0.1"/>
      <limit effort="25" lower="-1.27" upper="3.62" velocity="22"/>
    </joint>
    <link name="${name}_thigh">
      <visual>
        <origin rpy="0 0 0" xyz="0 0 0"/>
        <geometry>
          <xacro:if value="${side == 'R'}">
            <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_thigh_R.STL" scale="1 1 1"/>
          </xacro:if>
          <xacro:if value="${side == 'L'}">
            <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_thigh_L.STL" scale="1 1 1"/>
          </xacro:if>
        </geometry>
        <material name="grey"/>
      </visual>
      <collision>
        <origin rpy="0 1.5707963267948966 0" xyz="-0.015 0 -0.11"/>
        <geometry>
          <box size="0.1 0.03 0.04"/>
        </geometry>
      </collision>
      <inertial>
        <xacro:if value="${side == 'R'}">
          <origin rpy="0 0 0" xyz="-0.0033473586 0.031436598 -0.032009477"/>
        </xacro:if>
        <xacro:if value="${side == 'L'}">
          <origin rpy="0 0 0" xyz="-0.0033473586 -0.031436598 -0.032009477"/>
        </xacro:if>
        <mass value="1.5725954"/>
        <inertia ixx="0.0089600443" ixy="0.00020991626" ixz="-0.00051137678" iyy="0.0078167414" iyz="0.0017655451" izz="0.0028432732"/>
      </inertial>
    </link>
    <joint name="${name}_calf_joint" type="revolute">
      <origin rpy="0 0 0" xyz="0 0 -0.2"/>
      <parent link="${name}_thigh"/>
      <child link="${name}_calf"/>
      <axis xyz="0 1 0"/>
      <dynamics damping="0.01" friction="0.1"/>
      <limit effort="37.5" lower="-2.5" upper="-0.52" velocity="15"/>
    </joint>
    <link name="${name}_calf">
      <visual>
        <origin rpy="0 0 0" xyz="0 0 0"/>
        <geometry>
          <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_claf.STL" scale="1 1 1"/>
        </geometry>
        <material name="black"/>
      </visual>
      <collision>
        <origin rpy="0 0 0" xyz="0 0 0"/>
        <geometry>
          <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_claf_col.STL" scale="1 1 1"/>
        </geometry>
      </collision>
      <inertial>
        <origin rpy="0 0 0" xyz="0.0067810781 -4.6564496e-07 -0.070408595"/>
        <mass value="0.24182624"/>
        <inertia ixx="0.0016261823" ixy="1.1047432e-09" ixz="-7.1695784e-06" iyy="0.0016488489" iyz="-2.0151304e-09" izz="4.2153701e-05"/>
      </inertial>
    </link>

    <joint name="${name}_foot_fixed" type="fixed">
      <origin rpy="0 0 0" xyz="0 0 -0.2"/>
      <parent link="${name}_calf"/>
      <child link="${name}_foot"/>
    </joint>

    <link name="${name}_foot">
    </link>


    <gazebo reference="${name}_hip">
      <mu1>0.2</mu1>
      <mu2>0.2</mu2>
    </gazebo>

    <gazebo reference="${name}_thigh">
      <mu1>0.2</mu1>
      <mu2>0.2</mu2>
      <self_collide>1</self_collide>
      <kp value="1000000.0"/>
      <kd value="100.0"/>
    </gazebo>

    <gazebo reference="${name}_calf">
      <mu1>0.7</mu1>
      <mu2>0.7</mu2>
      <self_collide>1</self_collide>
      <kp value="1000000.0"/>
      <kd value="100.0"/>
    </gazebo>

  </xacro:macro>
</robot>