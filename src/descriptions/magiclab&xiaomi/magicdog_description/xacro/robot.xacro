<?xml version="1.0" ?>
<robot name="magicdog" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <xacro:arg name="GAZEBO" default="false"/>

  <xacro:if value="$(arg GAZEBO)">
    <xacro:include filename="gazebo.xacro"/>
  </xacro:if>

  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0.8 0.8 0.8 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="file://$(find magicdog_description)/meshes/magicdog/magicdog_base.STL" scale="1 1 1"/>
      </geometry>
      <material name="silver"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.327 0.194 0.114"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.043915809 0.0014386952 0.023430447"/>
      <mass value="7.1470505"/>
      <inertia ixx="0.041446738" ixy="0.00030043745" ixz="-0.029443965" iyy="0.13639022" iyz="0.00024507679" izz="0.13178939"/>
    </inertial>
  </link>
  <joint name="head_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0.25 0 0.14"/>
    <parent link="base"/>
    <child link="head"/>
  </joint>
  <link name="head">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.16 0.1 0.13"/>
      </geometry>
    </collision>
  </link>

  <joint name="imu_joint" type="fixed">
    <parent link="base"/>
    <child link="imu_link"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>

  <link name="imu_link">
    <inertial>
      <mass value="0.001"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>

  <xacro:include filename="leg.xacro"/>
  <xacro:magicdog_leg name="FR" side="R" front_hind="F" hip_xyz="0.220365 -0.04998785 0"/>
  <xacro:magicdog_leg name="FL" side="L" front_hind="F" hip_xyz="0.220365 0.04998785 0"/>
  <xacro:magicdog_leg name="RR" side="R" front_hind="R" hip_xyz="-0.220365 -0.04998785 0"/>
  <xacro:magicdog_leg name="RL" side="L" front_hind="R" hip_xyz="-0.220365 0.04998785 0"/>
</robot>

