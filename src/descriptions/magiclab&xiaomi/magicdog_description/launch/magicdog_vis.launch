<launch>
    <arg name="robot" default="magicdog"/>

    <param name="robot_description" command="$(find xacro)/xacro '$(find magicdog_description)/urdf/$(arg robot).urdf'"/>
    
    <node pkg="joint_state_publisher_gui" type="joint_state_publisher_gui" name="joint_state_publisher_gui">
        <param name="use_gui" value="TRUE"/>
    </node>

    <node pkg="robot_state_publisher" type="robot_state_publisher" name="robot_state_publisher">
        <param name="publish_frequency" type="double" value="1000.0"/>
    </node>

    <node pkg="rviz" type="rviz" name="rviz" respawn="false" output="screen" args="-d $(find magicdog_description)/rviz/simple_vis.rviz"/>

</launch>