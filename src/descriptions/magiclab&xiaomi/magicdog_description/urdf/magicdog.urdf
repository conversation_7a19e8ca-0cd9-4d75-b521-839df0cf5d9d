<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot.xacro                    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="magicdog">
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0. 0. 0. 0.3"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.4235294117647059 0.0392156862745098 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.8705882352941177 0.8117647058823529 0.7647058823529411 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_base.STL" scale="1 1 1"/>
      </geometry>
      <material name="silver"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.327 0.194 0.114"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.043915809 0.0014386952 0.023430447"/>
      <mass value="7.1470505"/>
      <inertia ixx="0.041446738" ixy="0.00030043745" ixz="-0.029443965" iyy="0.13639022" iyz="0.00024507679" izz="0.13178939"/>
    </inertial>
  </link>
  <joint name="head_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0.25 0 0.14"/>
    <parent link="base"/>
    <child link="head"/>
  </joint>
  <link name="head">
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.16 0.1 0.13"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.220365 -0.04998785 0"/>
    <parent link="base"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-0.6457718232379019" upper="0.7853981633974483" velocity="22"/>
  </joint>
  <link name="FR_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_hip_R.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.041" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.034672887 0.0039130227 0.0"/>
      <mass value="0.35996359"/>
      <inertia ixx="0.00010289238" ixy="-3.0677028e-05" ixz="0" iyy="0.00047667644" iyz="-0.0" izz="0.00046973335"/>
    </inertial>
  </link>
  <joint name="FR_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.07500000000000001 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="FR_thigh_shoulder">
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.07" radius="0.05"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.099 0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-1.2653637076958888" upper="3.6215581978882336" velocity="22"/>
  </joint>
  <link name="FR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_thigh_R.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.015 0 -0.11000000000000001"/>
      <geometry>
        <box size="0.1 0.03 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0033473586 0.031436598 -0.032009477"/>
      <mass value="1.5725954"/>
      <inertia ixx="0.0089600443" ixy="-0.00020991626" ixz="-0.00051137678" iyy="0.0078167414" iyz="-0.0017655451" izz="0.0028432732"/>
    </inertial>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="37.5" lower="-2.4958208303518914" upper="-0.5235987755982988" velocity="15"/>
  </joint>
  <link name="FR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf_col.STL" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0067810781 -4.6564496e-07 -0.070408595"/>
      <mass value="0.24182624"/>
      <inertia ixx="0.0016261823" ixy="1.1047432e-09" ixz="-7.1695784e-06" iyy="0.0016488489" iyz="-2.0151304e-09" izz="4.2153701e-05"/>
    </inertial>
  </link>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.220365 0.04998785 0"/>
    <parent link="base"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-0.7853981633974483" upper="0.6457718232379019" velocity="22"/>
  </joint>
  <link name="FL_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_hip_L.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.041" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.034672887 -0.0039130227 0.0"/>
      <mass value="0.35996359"/>
      <inertia ixx="0.00010289238" ixy="3.0677028e-05" ixz="0" iyy="0.00047667644" iyz="0.0" izz="0.00046973335"/>
    </inertial>
  </link>
  <joint name="FL_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.07500000000000001 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="FL_thigh_shoulder">
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.07" radius="0.05"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.099 0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-1.2653637076958888" upper="3.6215581978882336" velocity="22"/>
  </joint>
  <link name="FL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_thigh_L.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.015 0 -0.11000000000000001"/>
      <geometry>
        <box size="0.1 0.03 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0033473586 -0.031436598 -0.032009477"/>
      <mass value="1.5725954"/>
      <inertia ixx="0.0089600443" ixy="0.00020991626" ixz="-0.00051137678" iyy="0.0078167414" iyz="0.0017655451" izz="0.0028432732"/>
    </inertial>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="37.5" lower="-2.4958208303518914" upper="-0.5235987755982988" velocity="15"/>
  </joint>
  <link name="FL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf_col.STL" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0067810781 -4.6564496e-07 -0.070408595"/>
      <mass value="0.24182624"/>
      <inertia ixx="0.0016261823" ixy="1.1047432e-09" ixz="-7.1695784e-06" iyy="0.0016488489" iyz="-2.0151304e-09" izz="4.2153701e-05"/>
    </inertial>
  </link>
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.220365 -0.04998785 0"/>
    <parent link="base"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-0.6457718232379019" upper="0.7853981633974483" velocity="22"/>
  </joint>
  <link name="RR_hip">
    <visual>
      <origin rpy="0 3.1415926 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_hip_R.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.041" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.034672887 0.0039130227 0.0"/>
      <mass value="0.35996359"/>
      <inertia ixx="0.00010289238" ixy="3.0677028e-05" ixz="0" iyy="0.00047667644" iyz="-0.0" izz="0.00046973335"/>
    </inertial>
  </link>
  <joint name="RR_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.07500000000000001 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="RR_thigh_shoulder">
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.07" radius="0.05"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.099 0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-1.2653637076958888" upper="3.6215581978882336" velocity="22"/>
  </joint>
  <link name="RR_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_thigh_R.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.015 0 -0.11000000000000001"/>
      <geometry>
        <box size="0.1 0.03 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0033473586 0.031436598 -0.032009477"/>
      <mass value="1.5725954"/>
      <inertia ixx="0.0089600443" ixy="-0.00020991626" ixz="-0.00051137678" iyy="0.0078167414" iyz="-0.0017655451" izz="0.0028432732"/>
    </inertial>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="37.5" lower="-2.4958208303518914" upper="-0.5235987755982988" velocity="15"/>
  </joint>
  <link name="RR_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf_col.STL" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0067810781 -4.6564496e-07 -0.070408595"/>
      <mass value="0.24182624"/>
      <inertia ixx="0.0016261823" ixy="1.1047432e-09" ixz="-7.1695784e-06" iyy="0.0016488489" iyz="-2.0151304e-09" izz="4.2153701e-05"/>
    </inertial>
  </link>
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.220365 0.04998785 0"/>
    <parent link="base"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-0.7853981633974483" upper="0.6457718232379019" velocity="22"/>
  </joint>
  <link name="RL_hip">
    <visual>
      <origin rpy="0 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_hip_L.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.041" radius="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.034672887 -0.0039130227 0.0"/>
      <mass value="0.35996359"/>
      <inertia ixx="0.00010289238" ixy="-3.0677028e-05" ixz="0" iyy="0.00047667644" iyz="0.0" izz="0.00046973335"/>
    </inertial>
  </link>
  <joint name="RL_hip_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.07500000000000001 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh_shoulder"/>
  </joint>
  <!-- this link is only for collision -->
  <link name="RL_thigh_shoulder">
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.07" radius="0.05"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.099 0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="25" lower="-1.2653637076958888" upper="3.6215581978882336" velocity="22"/>
  </joint>
  <link name="RL_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_thigh_L.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.015 0 -0.11000000000000001"/>
      <geometry>
        <box size="0.1 0.03 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0033473586 -0.031436598 -0.032009477"/>
      <mass value="1.5725954"/>
      <inertia ixx="0.0089600443" ixy="0.00020991626" ixz="-0.00051137678" iyy="0.0078167414" iyz="0.0017655451" izz="0.0028432732"/>
    </inertial>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 -0.2"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0" friction="0"/>
    <limit effort="37.5" lower="-2.4958208303518914" upper="-0.5235987755982988" velocity="15"/>
  </joint>
  <link name="RL_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf.STL" scale="1 1 1"/>
      </geometry>
      <material name="grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://magicdog_description/meshes/magicdog/magicdog_claf_col.STL" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0067810781 -4.6564496e-07 -0.070408595"/>
      <mass value="0.24182624"/>
      <inertia ixx="0.0016261823" ixy="1.1047432e-09" ixz="-7.1695784e-06" iyy="0.0016488489" iyz="-2.0151304e-09" izz="4.2153701e-05"/>
    </inertial>
  </link>
</robot>

