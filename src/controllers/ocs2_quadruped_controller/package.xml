<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>ocs2_quadruped_controller</name>
  <version>0.0.0</version>
  <description>A ROS2-Control quadruped controller based on OCS2 library</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>backward_ros</depend>
  <depend>controller_interface</depend>
  <depend>controller_common</depend>
  <depend>pluginlib</depend>
  <depend>control_input_msgs</depend>
  <depend>qpoases_colcon</depend>
  <depend>ocs2_self_collision</depend>
  <depend>ocs2_legged_robot_ros</depend>
  <depend>angles</depend>

  <!--/ Dependencies for Perceptive Mode -->
  <depend>convex_plane_decomposition_msgs</depend>
  <depend>convex_plane_decomposition_ros</depend>
  <depend>grid_map_sdf</depend>
  <depend>ocs2_sphere_approximation</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
