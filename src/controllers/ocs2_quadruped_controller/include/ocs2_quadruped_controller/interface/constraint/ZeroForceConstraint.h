/******************************************************************************
Copyright (c) 2021, <PERSON><PERSON>d Far<PERSON>dian. All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

 * Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

 * Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

 * Neither the name of the copyright holder nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONT<PERSON>BUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
******************************************************************************/

#pragma once

#include <ocs2_centroidal_model/CentroidalModelInfo.h>
#include <ocs2_core/constraint/StateInputConstraint.h>

#include "../SwitchedModelReferenceManager.h"

namespace ocs2::legged_robot {
    class ZeroForceConstraint final : public StateInputConstraint {
    public:
        /*
         * Constructor
         * @param [in] referenceManager : Switched model ReferenceManager.
         * @param [in] contactPointIndex : The 3 DoF contact index.
         * @param [in] info : The centroidal model information.
         */
        ZeroForceConstraint(const SwitchedModelReferenceManager &referenceManager, size_t contactPointIndex,
                            CentroidalModelInfo info);

        ~ZeroForceConstraint() override = default;

        ZeroForceConstraint *clone() const override { return new ZeroForceConstraint(*this); }

        bool isActive(scalar_t time) const override;

        size_t getNumConstraints(scalar_t time) const override { return 3; }

        vector_t getValue(scalar_t time, const vector_t &state, const vector_t &input,
                          const PreComputation &preComp) const override;

        VectorFunctionLinearApproximation getLinearApproximation(scalar_t time, const vector_t &state,
                                                                 const vector_t &input,
                                                                 const PreComputation &preComp) const override;

    private:
        ZeroForceConstraint(const ZeroForceConstraint &other) = default;

        const SwitchedModelReferenceManager *referenceManagerPtr_;
        const size_t contactPointIndex_;
        const CentroidalModelInfo info_;
    };
}
