//
// Created by <PERSON><PERSON> on 24-10-6.
//

#include "rl_quadruped_controller/FSM/StateRL.h"
#include <ament_index_cpp/get_package_share_directory.hpp>
#include <rclcpp/logging.hpp>
#include <yaml-cpp/yaml.h>
#include <cmath>
#include <chrono>


// utils_yaml.hpp  或直接写在 StateRL.cpp 开头
template <typename T>
T readOr(const YAML::Node& cfg,
         const char* key,
         const T& def,
         const rclcpp::Logger& log)
{
    if (!cfg[key]) {
        RCLCPP_WARN(log, "YAML key '%s' missing, use default", key);
        return def;
    }
    try {
        return cfg[key].as<T>();
    }
    catch (const YAML::BadConversion&) {
        RCLCPP_ERROR(log, "YAML key '%s' type mismatch, use default", key);
        return def;
    }
}

template <typename T>
std::vector<T> ReadVectorFromYaml(const YAML::Node& node)
{
    std::vector<T> values;
    for (const auto& val : node)
    {
        values.push_back(val.as<T>());
    }
    return values;
}

StateRL::StateRL(CtrlInterfaces& ctrl_interfaces,
                 CtrlComponent& ctrl_component,
                 const std::vector<double>& target_pos) :
    FSMState(FSMStateName::RL, "rl", ctrl_interfaces),
    node_(ctrl_component.node_),
    enable_estimator_(ctrl_component.enable_estimator_),
    estimator_(ctrl_component.estimator_)
{
    node_->declare_parameter("robot_pkg", robot_pkg_);
    node_->declare_parameter("model_folder", model_folder_);
    node_->declare_parameter("use_rl_thread", use_rl_thread_);
    robot_pkg_ = node_->get_parameter("robot_pkg").as_string();
    model_folder_ = node_->get_parameter("model_folder").as_string();
    use_rl_thread_ = node_->get_parameter("use_rl_thread").as_bool();

    RCLCPP_INFO(node_->get_logger(), "Using robot model from %s", robot_pkg_.c_str());
    const std::string package_share_directory = ament_index_cpp::get_package_share_directory(robot_pkg_);
    const std::string model_path = package_share_directory + "/config/" + model_folder_;

    for (int i = 0; i < 12; i++)
    {
        init_pos_[i] = target_pos[i];
    }

    // read params from yaml
    loadYaml(model_path);

    if (!params_.observations_history.empty())
    {
        history_obs_buf_ = std::make_shared<ObservationBuffer>(1, params_.num_single_obs,
                                                               params_.observations_history.size());
    }

    RCLCPP_INFO(node_->get_logger(), "Model loading: %s", params_.model_name.c_str());
    model_ = torch::jit::load(model_path + "/" + params_.model_name);


    // for (const auto &param: model_.parameters()) {
    //     std::cout << "Parameter dtype: " << param.dtype() << std::endl;
    // }
     /* ---------- 4. 新增：关节输出偏置动态参数 ---------- */
    bias_ = std::vector<double>(params_.num_of_dofs, 0.0);          // 初值全 0
    node_->declare_parameter("output_bias", bias_);
    bias_buf_.writeFromNonRT(bias_);                                // 写入实时缓冲

    cb_handle_ = node_->add_on_set_parameters_callback(             // 动态参数回调
        [this](const std::vector<rclcpp::Parameter>& params)
        {
            rcl_interfaces::msg::SetParametersResult res;
            res.successful = true;

            for (const auto& prm : params)
            {
                if (prm.get_name() == "output_bias")
                {
                    auto v = prm.as_double_array();
                    if (v.size() != static_cast<std::size_t>(params_.num_of_dofs)) {
                        res.successful = false;
                        res.reason     = "size mismatch";
                        return res;
                    }
                    bias_buf_.writeFromNonRT(v);                    // 非实时写
                }
            }
            return res;                                             // Jazzy 写法
        });


    if (use_rl_thread_)
    {
        rl_thread_ = std::thread([&]{
            while (true)
            {
                try
                {
                    executeAndSleep(
                        [&]
                        {
                            if (running_)
                            {
                                runModel();
                            }
                        },
                        ctrl_interfaces_.frequency_ / params_.decimation);
                }
                catch (const std::exception& e)
                {
                    running_ = false;
                    RCLCPP_ERROR(rclcpp::get_logger("StateRL"), "Error in RL thread: %s", e.what());
                }
            }
        });
        setThreadPriority(60, rl_thread_);
    }
}

void StateRL::enter()
{
    // Init observations
    obs_.lin_vel = torch::tensor({{0.0, 0.0, 0.0}});
    obs_.ang_vel = torch::tensor({{0.0, 0.0, 0.0}});
    obs_.gravity_vec = torch::tensor({{0.0, 0.0, -1.0}});
    obs_.commands = torch::tensor({{0.0, 0.0, 0.0}});
    obs_.base_quat = torch::tensor({{0.0, 0.0, 0.0, 1.0}});
    obs_.dof_pos = params_.default_dof_pos;
    obs_.dof_vel = torch::zeros({1, params_.num_of_dofs});
    obs_.actions = torch::zeros({1, params_.num_of_dofs});

    // GO2_Trot specific observations
    obs_.base_euler = torch::tensor({{0.0, 0.0, 0.0}});
    obs_.current_time = 0.0;

    // Init output
    output_torques = torch::zeros({1, params_.num_of_dofs});
    output_dof_pos_ = params_.default_dof_pos;

    // Init control
    control_.x = 0.0;
    control_.y = 0.0;
    control_.yaw = 0.0;

    // history
    if (!params_.observations_history.empty()) {
        history_obs_buf_->clear();
    }

    running_ = true;
}

void StateRL::run(const rclcpp::Time&/*time*/, const rclcpp::Duration&/*period*/)
{
    getState();
    if (!use_rl_thread_)
    {
        runModel();
    }
    setCommand();
}

void StateRL::exit()
{
    running_ = false;
}

FSMStateName StateRL::checkChange()
{
    if (enable_estimator_ and !estimator_->safety())
    {
        return FSMStateName::PASSIVE;
    }
    switch (ctrl_interfaces_.control_inputs_.command)
    {
    case 1:
        return FSMStateName::PASSIVE;
    case 2:
        return FSMStateName::FIXEDDOWN;
    default:
        return FSMStateName::RL;
    }
}

bool StateRL::isNewStylePolicy() const
{
    // Check if this policy uses new-style observations (GO2_Trot)
    // New-style policies have specific observation types like "sin_time", "cos_time", etc.
    for (const std::string& obs : params_.observations)
    {
        if (obs == "sin_time" || obs == "cos_time" || obs == "lin_vel_cmd_x" ||
            obs == "lin_vel_cmd_y" || obs == "ang_vel_cmd_yaw" || obs == "base_orientation")
        {
            return true;
        }
    }
    return false;
}

torch::Tensor StateRL::computeSingleObservation()
{
    std::vector<torch::Tensor> obs_list;

    for (const std::string& observation : params_.observations)
    {
        // New-style observations (GO2_Trot)
        if (observation == "sin_time")
        {
            // Gait phase sine signal
            double phase = fmod(obs_.current_time, params_.cycle_time) / params_.cycle_time;
            double sin_val = sin(2.0 * M_PI * phase);
            obs_list.push_back(torch::tensor({{sin_val}}));
        }
        else if (observation == "cos_time")
        {
            // Gait phase cosine signal
            double phase = fmod(obs_.current_time, params_.cycle_time) / params_.cycle_time;
            double cos_val = cos(2.0 * M_PI * phase);
            obs_list.push_back(torch::tensor({{cos_val}}));
        }
        else if (observation == "lin_vel_cmd_x")
        {
            obs_list.push_back(obs_.commands.index({torch::indexing::Slice(), 0}).unsqueeze(1) * params_.lin_vel_scale);
        }
        else if (observation == "lin_vel_cmd_y")
        {
            obs_list.push_back(obs_.commands.index({torch::indexing::Slice(), 1}).unsqueeze(1) * params_.lin_vel_scale);
        }
        else if (observation == "ang_vel_cmd_yaw")
        {
            obs_list.push_back(obs_.commands.index({torch::indexing::Slice(), 2}).unsqueeze(1) * params_.ang_vel_scale);
        }
        else if (observation == "base_orientation")
        {
            // Use euler angles instead of gravity vector
            obs_list.push_back(obs_.base_euler);
        }
        // Common observations (both old and new style)
        else if (observation == "lin_vel")
        {
            obs_list.push_back(obs_.lin_vel * params_.lin_vel_scale);
        }
        else if (observation == "ang_vel")
        {
            obs_list.push_back(obs_.ang_vel * params_.ang_vel_scale);
        }
        else if (observation == "gravity_vec")
        {
            obs_list.push_back(quatRotateInverse(obs_.base_quat, obs_.gravity_vec, params_.framework));
        }
        else if (observation == "commands")
        {
            obs_list.push_back(obs_.commands * params_.commands_scale);
        }
        else if (observation == "dof_pos")
        {
            obs_list.push_back((obs_.dof_pos - params_.default_dof_pos) * params_.dof_pos_scale);
        }
        else if (observation == "dof_vel")
        {
            obs_list.push_back(obs_.dof_vel * params_.dof_vel_scale);
        }
        else if (observation == "actions")
        {
            obs_list.push_back(obs_.actions);
        }
    }

    const torch::Tensor obs = cat(obs_list, 1);

    // std::cout << "Single Observation: " << obs << std::endl;
    torch::Tensor clamped_obs = clamp(obs, -params_.clip_obs, params_.clip_obs);
    return clamped_obs;
}

torch::Tensor StateRL::computeObservation()
{
    if (!params_.observations_history.empty())
    {
        // For frame stacking, return the stacked observations from buffer
        return history_obs_;
    }
    else
    {
        // For non-frame stacking, return single observation
        return computeSingleObservation();
    }
}

void StateRL::loadYaml(const std::string& config_path)
{
    YAML::Node config;
    try {
        config = YAML::LoadFile(config_path + "/config.yaml");
    } catch (YAML::BadFile& e) {
        RCLCPP_ERROR(node_->get_logger(),
                     "StateRL::loadYaml() - cannot open %s/config.yaml",
                     config_path.c_str());
        return;
    }

    const auto& log = node_->get_logger();   // 或 auto log = …

    /* ---------- 基本信息 ---------- */
    params_.model_name = readOr<std::string>(config, "model_name", "policy.pt", log);
    params_.framework  = readOr<std::string>(config, "framework", "isaacgym", log);

    /* ---------- 帧堆叠 / 维度 ---------- */
    params_.observations_history =
        config["observations_history"].IsNull()
            ? std::vector<int>{}
            : ReadVectorFromYaml<int>(config["observations_history"]);

    params_.decimation = readOr<int>(config, "decimation", 4, log);

    // 兼容旧 / 新格式
    if (config["num_single_obs"]) {
        params_.num_single_obs   = config["num_single_obs"].as<int>();
        params_.num_observations = readOr<int>(config, "num_observations",
                                               params_.num_single_obs *
                                               static_cast<int>(params_.observations_history.size()), log);
    } else {
        params_.num_observations = readOr<int>(config, "num_observations", 0, log);
        params_.num_single_obs   = params_.num_observations;   // 无帧堆叠
    }

    /* ---------- 观测 / 动作缩放 ---------- */
    params_.observations = ReadVectorFromYaml<std::string>(
                               config["observations"].IsDefined()
                                   ? config["observations"]
                                   : YAML::Node(YAML::NodeType::Sequence));   // 空序列

    params_.clip_obs   = readOr<double>(config, "clip_obs", 100.0, log);
    params_.action_scale = readOr<double>(config, "action_scale", 1.0, log);

    params_.hip_scale_reduction =
        readOr<double>(config, "hip_scale_reduction", 1.0, log);

    params_.hip_scale_reduction_indices =
        config["hip_scale_reduction_indices"].IsNull()
            ? std::vector<int>{}
            : ReadVectorFromYaml<int>(config["hip_scale_reduction_indices"]);

    /* ---------- 下限 / 上限 ---------- */
    auto readTensorOrEmpty = [&](const char* key) {
        return config[key].IsNull()
            ? torch::tensor({}).view({1, -1})
            : torch::tensor(ReadVectorFromYaml<double>(config[key])).view({1, -1});
    };
    params_.clip_actions_lower = readTensorOrEmpty("clip_actions_lower");
    params_.clip_actions_upper = readTensorOrEmpty("clip_actions_upper");

    /* ---------- 尺度系数 ---------- */
    params_.num_of_dofs  = readOr<int>(config, "num_of_dofs", 12, log);
    params_.lin_vel_scale = readOr<double>(config, "lin_vel_scale", 2.0, log);
    params_.ang_vel_scale = readOr<double>(config, "ang_vel_scale", 0.25, log);
    params_.dof_pos_scale = readOr<double>(config, "dof_pos_scale", 1.0, log);
    params_.dof_vel_scale = readOr<double>(config, "dof_vel_scale", 0.05, log);

    if (config["commands_scale"].IsNull()) {
        params_.commands_scale =
            torch::tensor({params_.lin_vel_scale,
                           params_.lin_vel_scale,
                           params_.ang_vel_scale});
    } else {
        params_.commands_scale =
            torch::tensor(ReadVectorFromYaml<double>(config["commands_scale"]));
    }

    /* ---------- PD 增益 / 力矩限幅 ---------- */
    auto view1xn = [](const std::vector<double>& v) {
        return torch::tensor(v).view({1, -1});
    };
    params_.rl_kp        = view1xn(readOr<std::vector<double>>(config, "rl_kp",
                                 std::vector<double>(params_.num_of_dofs, 40.0), log));
    params_.rl_kd        = view1xn(readOr<std::vector<double>>(config, "rl_kd",
                                 std::vector<double>(params_.num_of_dofs, 1.0), log));
    params_.torque_limits = view1xn(readOr<std::vector<double>>(config, "torque_limits",
                                  std::vector<double>(params_.num_of_dofs, 33.5), log));

    /* ---------- GO2_Trot 专属 ---------- */
    params_.cycle_time = readOr<double>(config, "cycle_time", 0.5, log);

    /* ---------- 默认关节位姿 ---------- */
    params_.default_dof_pos =
        torch::from_blob(init_pos_, {params_.num_of_dofs}, torch::kDouble)
            .clone().to(torch::kFloat).unsqueeze(0);

    /* ---------- 维度一致性自检 ---------- */
    const int expected = params_.num_single_obs *
                         (params_.observations_history.empty()
                              ? 1
                              : static_cast<int>(params_.observations_history.size()));
    if (expected != params_.num_observations) {
        RCLCPP_ERROR(log,
            "num_observations (%d) != num_single_obs (%d) × frame_stack (%zu)",
            params_.num_observations, params_.num_single_obs,
            params_.observations_history.size());
        throw std::runtime_error("StateRL: observation dim mismatch");
    }
}

torch::Tensor StateRL::quatRotateInverse(const torch::Tensor& q, const torch::Tensor& v, const std::string& framework)
{
    torch::Tensor q_w;
    torch::Tensor q_vec;
    if (framework == "isaacsim")
    {
        q_w = q.index({torch::indexing::Slice(), 0});
        q_vec = q.index({torch::indexing::Slice(), torch::indexing::Slice(1, 4)});
    }
    else if (framework == "isaacgym")
    {
        q_w = q.index({torch::indexing::Slice(), 3});
        q_vec = q.index({torch::indexing::Slice(), torch::indexing::Slice(0, 3)});
    }
    const c10::IntArrayRef shape = q.sizes();

    const torch::Tensor a = v * (2.0 * torch::pow(q_w, 2) - 1.0).unsqueeze(-1);
    const torch::Tensor b = cross(q_vec, v, -1) * q_w.unsqueeze(-1) * 2.0;
    const torch::Tensor c = q_vec * bmm(q_vec.view({shape[0], 1, 3}), v.view({shape[0], 3, 1})).squeeze(-1) * 2.0;
    return a - b + c;
}

torch::Tensor StateRL::quatToEuler(const torch::Tensor& quat)
{
    // Convert quaternion to euler angles (roll, pitch, yaw)
    // Input quat format: [x, y, z, w] for isaacgym
    torch::Tensor x = quat.index({torch::indexing::Slice(), 0});
    torch::Tensor y = quat.index({torch::indexing::Slice(), 1});
    torch::Tensor z = quat.index({torch::indexing::Slice(), 2});
    torch::Tensor w = quat.index({torch::indexing::Slice(), 3});

    // Roll (x-axis rotation)
    torch::Tensor t0 = 2.0 * (w * x + y * z);
    torch::Tensor t1 = 1.0 - 2.0 * (x * x + y * y);
    torch::Tensor roll = torch::atan2(t0, t1);

    // Pitch (y-axis rotation)
    torch::Tensor t2 = 2.0 * (w * y - z * x);
    t2 = torch::clamp(t2, -1.0, 1.0);
    torch::Tensor pitch = torch::asin(t2);

    // Yaw (z-axis rotation)
    torch::Tensor t3 = 2.0 * (w * z + x * y);
    torch::Tensor t4 = 1.0 - 2.0 * (y * y + z * z);
    torch::Tensor yaw = torch::atan2(t3, t4);

    return torch::stack({roll, pitch, yaw}, -1);
}

torch::Tensor StateRL::forward()
{
    torch::autograd::GradMode::set_enabled(false);
    torch::Tensor actions;

    if (!params_.observations_history.empty())
    {
        // For frame stacking: insert single observation (47-dim) into buffer
        torch::Tensor single_obs = computeSingleObservation();
        history_obs_buf_->insert(single_obs);
        history_obs_ = history_obs_buf_->getObsVec(params_.observations_history);
        actions = model_.forward({history_obs_}).toTensor();
    }
    else
    {
        // For non-frame stacking: use single observation directly
        torch::Tensor single_obs = computeSingleObservation();
        actions = model_.forward({single_obs}).toTensor();
    }

    if (params_.clip_actions_upper.numel() != 0 && params_.clip_actions_lower.numel() != 0)
    {
        return clamp(actions, params_.clip_actions_lower, params_.clip_actions_upper);
    }
    return actions;
}

void StateRL::getState()
{
    if (params_.framework == "isaacgym")
    {
        robot_state_.imu.quaternion[3] = ctrl_interfaces_.imu_state_interface_[0].get().get_optional().value();
        robot_state_.imu.quaternion[0] = ctrl_interfaces_.imu_state_interface_[1].get().get_optional().value();
        robot_state_.imu.quaternion[1] = ctrl_interfaces_.imu_state_interface_[2].get().get_optional().value();
        robot_state_.imu.quaternion[2] = ctrl_interfaces_.imu_state_interface_[3].get().get_optional().value();
    }
    else if (params_.framework == "isaacsim")
    {
        robot_state_.imu.quaternion[0] = ctrl_interfaces_.imu_state_interface_[0].get().get_optional().value();
        robot_state_.imu.quaternion[1] = ctrl_interfaces_.imu_state_interface_[1].get().get_optional().value();
        robot_state_.imu.quaternion[2] = ctrl_interfaces_.imu_state_interface_[2].get().get_optional().value();
        robot_state_.imu.quaternion[3] = ctrl_interfaces_.imu_state_interface_[3].get().get_optional().value();
    }

    robot_state_.imu.gyroscope[0] = ctrl_interfaces_.imu_state_interface_[4].get().get_optional().value();
    robot_state_.imu.gyroscope[1] = ctrl_interfaces_.imu_state_interface_[5].get().get_optional().value();
    robot_state_.imu.gyroscope[2] = ctrl_interfaces_.imu_state_interface_[6].get().get_optional().value();

    robot_state_.imu.accelerometer[0] = ctrl_interfaces_.imu_state_interface_[7].get().get_optional().value();
    robot_state_.imu.accelerometer[1] = ctrl_interfaces_.imu_state_interface_[8].get().get_optional().value();
    robot_state_.imu.accelerometer[2] = ctrl_interfaces_.imu_state_interface_[9].get().get_optional().value();

    for (int i = 0; i < 12; i++)
    {
        robot_state_.motor_state.q[i] = ctrl_interfaces_.joint_position_state_interface_[i].get().get_optional().
            value();
        robot_state_.motor_state.dq[i] = ctrl_interfaces_.joint_velocity_state_interface_[i].get().get_optional().
            value();
        robot_state_.motor_state.tauEst[i] = ctrl_interfaces_.joint_effort_state_interface_[i].get().get_optional().
            value();
    }

    control_.x = ctrl_interfaces_.control_inputs_.ly;
    control_.y = -ctrl_interfaces_.control_inputs_.lx;
    control_.yaw = -ctrl_interfaces_.control_inputs_.rx;

    updated_ = true;
}

void StateRL::runModel()
{
    // Update time for gait phase calculation (only for new-style policies)
    if (isNewStylePolicy())
    {
        static auto start_time = std::chrono::high_resolution_clock::now();
        auto current_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(current_time - start_time);
        obs_.current_time = duration.count() / 1000000.0;  // convert to seconds

        // Convert quaternion to euler angles for new-style policies
        obs_.base_euler = quatToEuler(obs_.base_quat);
    }

    if (enable_estimator_)
    {
        obs_.lin_vel = torch::from_blob(estimator_->getVelocity().data(), {3}, torch::kDouble).clone().
            to(torch::kFloat).unsqueeze(0);
    }
    obs_.ang_vel = torch::tensor(robot_state_.imu.gyroscope).unsqueeze(0);
    obs_.commands = torch::tensor({{control_.x, control_.y, control_.yaw}});
    obs_.base_quat = torch::tensor(robot_state_.imu.quaternion).unsqueeze(0);
    obs_.dof_pos = torch::tensor(robot_state_.motor_state.q).narrow(0, 0, params_.num_of_dofs).unsqueeze(0);
    obs_.dof_vel = torch::tensor(robot_state_.motor_state.dq).narrow(0, 0, params_.num_of_dofs).unsqueeze(0);

    const torch::Tensor clamped_actions = forward();

    for (const int i : params_.hip_scale_reduction_indices)
    {
        clamped_actions[0][i] *= params_.hip_scale_reduction;
    }

    obs_.actions = clamped_actions;

    const torch::Tensor actions_scaled = clamped_actions * params_.action_scale;
    // torch::Tensor output_torques = params_.rl_kp * (actions_scaled + params_.default_dof_pos - obs_.dof_pos) - params_.rl_kd * obs_.dof_vel;
    // output_torques = clamp(output_torques, -(params_.torque_limits), params_.torque_limits);

    const auto& b_vec = bias_buf_.readFromRT();
    torch::Tensor bias_tensor = torch::from_blob(const_cast<double*>(b_vec->data()),
                                                {params_.num_of_dofs},
                                                torch::kDouble).clone().to(torch::kFloat).unsqueeze(0);



    output_dof_pos_ = actions_scaled + params_.default_dof_pos + bias_tensor;

    for (int i = 0; i < params_.num_of_dofs; ++i)
    {
        robot_command_.motor_command.q[i] = output_dof_pos_[0][i].item<double>();
        robot_command_.motor_command.dq[i] = 0;
        robot_command_.motor_command.kp[i] = params_.rl_kp[0][i].item<double>();
        robot_command_.motor_command.kd[i] = params_.rl_kd[0][i].item<double>();
        robot_command_.motor_command.tau[i] = 0;
    }
}

void StateRL::setCommand() const
{
    for (int i = 0; i < 12; i++)
    {
        std::ignore = ctrl_interfaces_.joint_position_command_interface_[i].get().
                                                                            set_value(
                                                                                robot_command_.motor_command.q[i]);
        std::ignore = ctrl_interfaces_.joint_velocity_command_interface_[i].get().set_value(
            robot_command_.motor_command.dq[i]);
        std::ignore = ctrl_interfaces_.joint_kp_command_interface_[i].get().set_value(
            robot_command_.motor_command.kp[i]);
        std::ignore = ctrl_interfaces_.joint_kd_command_interface_[i].get().set_value(
            robot_command_.motor_command.kd[i]);
        std::ignore = ctrl_interfaces_.joint_torque_command_interface_[i].get().
                                                                          set_value(
                                                                              robot_command_.motor_command.tau[i]);
    }
}
