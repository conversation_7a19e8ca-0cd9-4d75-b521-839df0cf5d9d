<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
    <name>unitree_guide_controller</name>
    <version>0.0.0</version>
    <description>A Ros2 Control Controller based on Unitree Guide</description>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <license>Apache-2.0</license>

    <buildtool_depend>ament_cmake</buildtool_depend>

    <depend>backward_ros</depend>
    <depend>controller_interface</depend>
    <depend>controller_common</depend>
    <depend>pluginlib</depend>
    <depend>control_input_msgs</depend>
    <depend>kdl_parser</depend>

    <exec_depend>controller_manager</exec_depend>
    <exec_depend>joint_state_broadcaster</exec_depend>

    <test_depend>ament_lint_auto</test_depend>
    <test_depend>ament_lint_common</test_depend>

    <export>
        <build_type>ament_cmake</build_type>
    </export>
</package>
