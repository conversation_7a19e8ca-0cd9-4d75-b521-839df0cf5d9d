/*
 File $Id: QuadProg++.hh 232 2007-06-21 12:29:00Z digasper $

 The quadprog_solve() function implements the algorithm of <PERSON>far<PERSON> and <PERSON><PERSON><PERSON>
 for the solution of a (convex) Quadratic Programming problem
 by means of an active-set dual method.

The problem is in the form:

min 0.5 * x G x + g0 x
s.t.
    CE^T x + ce0 = 0
    CI^T x + ci0 >= 0

 The matrix and vectors dimensions are as follows:
     G: n * n
                g0: n

                CE: n * p
         ce0: p

          CI: n * m
   ci0: m

     x: n

 The function will return the cost of the solution written in the x vector or
 std::numeric_limits::infinity() if the problem is infeasible. In the latter
case the value of the x vector is not correct.

 References: <PERSON><PERSON>, <PERSON><PERSON>. A numerically stable dual method for
solving strictly convex quadratic programs. Mathematical Programming 27 (1983)
pp. 1-33.

 Notes:
  1. pay attention in setting up the vectors ce0 and ci0.
           If the constraints of your problem are specified in the form
           A^T x = b and C^T x >= d, then you should set ce0 = -b and ci0 = -d.
  2. The matrices have column dimension equal to MATRIX_DIM,
     a constant set to 20 in this file (by means of a #define macro).
     If the matrices are bigger than 20 x 20 the limit could be
                 increased by means of a -DMATRIX_DIM=n on the compiler command
line.
  3. The matrix G is modified within the function since it is used to compute
     the G = L^T L cholesky factorization for further computations inside the
function. If you need the original matrix G you should make a copy of it and
pass the copy to the function.

 Author: Luca Di Gaspero
                         DIEGM - University of Udine, Italy
                                 <EMAIL>
                                 http://www.diegm.uniud.it/digaspero/

 The author will be grateful if the researchers using this software will
 acknowledge the contribution of this function in their research papers.

 Copyright (c) 2007-2016 Luca Di Gaspero

 This software may be modified and distributed under the terms
 of the MIT license.  See the LICENSE file for details.
*/

#ifndef _QUADPROGPP
#define _QUADPROGPP

#include "Array.hh"
#include <eigen3/Eigen/Dense>

namespace quadprogpp {

double solve_quadprog(Matrix<double>& G, Vector<double>& g0,
                      const Matrix<double>& CE, const Vector<double>& ce0,
                      const Matrix<double>& CI, const Vector<double>& ci0,
                      Vector<double>& x);

}  // namespace quadprogpp

#endif  // #define _QUADPROGPP
