#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from controller_manager_msgs.srv import SwitchController
from sensor_msgs.msg import Joy


class ControllerSwitcher(Node):
    def __init__(self):
        super().__init__('controller_switcher_node')

        # 控制器名称映射
        self.controller_map = {
            1: 'ocs2_quadruped_controller',
            2: 'unitree_guide_controller',
            3: 'rl_quadruped_controller'
        }

        self.active_controller = None  # 当前激活的控制器名
        self.switching = False         # 防止重复触发
        self.last_command = 0          # 上一次的 command 值

        # 创建服务客户端
        self.cli = self.create_client(SwitchController, '/controller_manager/switch_controller')
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('Waiting for controller_manager...')

        # Joy 订阅
        self.create_subscription(Joy, '/joy', self.joy_callback, 10)

    def joy_callback(self, msg: Joy):
        command = self.interpret_command(msg)

        # 防抖动 + 无重复切换
        if command == 0 or command == self.last_command or self.switching:
            return
        self.last_command = command

        # 切换控制器
        self.switching = True

        if command in self.controller_map:
            new_controller = self.controller_map[command]
            self.switch_controller(new_controller)
        elif command == 9:
            self.deactivate_all()

    def interpret_command(self, msg: Joy) -> int:
        """模仿你的 C++ 控制逻辑"""
        if msg.buttons[1] and msg.buttons[4]:  # LB + B
            return 1
        elif msg.buttons[0] and msg.buttons[4]:  # LB + A
            return 2
        elif msg.buttons[2] and msg.buttons[4]:  # LB + X
            return 3
        elif msg.buttons[7]:  # START
            return 9
        else:
            return 0

    def switch_controller(self, new_name):
        req = SwitchController.Request()
        req.activate_controllers = [new_name]
        if self.active_controller:
            req.deactivate_controllers = [self.active_controller]
        req.strictness = SwitchController.Request.STRICT

        future = self.cli.call_async(req)

        def callback(fut):
            if fut.result() and fut.result().ok:
                self.get_logger().info(f"Switched to controller: {new_name}")
                self.active_controller = new_name
            else:
                self.get_logger().error("Controller switch failed")
            self.switching = False

        future.add_done_callback(callback)

    def deactivate_all(self):
        if not self.active_controller:
            self.switching = False
            return

        req = SwitchController.Request()
        req.activate_controllers = []
        req.deactivate_controllers = [self.active_controller]
        req.strictness = SwitchController.Request.STRICT

        future = self.cli.call_async(req)

        def callback(fut):
            if fut.result() and fut.result().ok:
                self.get_logger().info(f"Deactivated controller: {self.active_controller}")
                self.active_controller = None
            else:
                self.get_logger().error("Deactivate failed")
            self.switching = False

        future.add_done_callback(callback)


def main(args=None):
    rclpy.init(args=args)
    node = ControllerSwitcher()
    rclpy.spin(node)
    rclpy.shutdown()
