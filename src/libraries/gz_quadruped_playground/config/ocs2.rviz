Panels:
  - Class: rviz_common/Displays
    Help Height: 270
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /TF1/Frames1
        - /Target Trajectories1/Target Feet Trajectories1/Marker1
        - /Target Trajectories1/Target Feet Trajectories1/Marker2
        - /Target Trajectories1/Target Feet Trajectories1/Marker3
        - /Target Trajectories1/Target Feet Trajectories1/Marker4
        - /Target Trajectories1/Target Base Trajectory1
        - /PointCloud22
      Splitter Ratio: 0.5
    Tree Height: 638
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz_common/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: PointCloud2
  - Class: rviz_common/Displays
    Help Height: 138
    Name: Displays
    Property Tree Widget:
      Expanded: ~
      Splitter Ratio: 0.8294117450714111
    Tree Height: 268
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz_default_plugins/Grid
      Color: 0; 0; 0
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 100
      Reference Frame: odom
      Value: true
    - Alpha: 0.800000011920929
      Class: rviz_default_plugins/RobotModel
      Collision Enabled: false
      Description File: ""
      Description Source: Topic
      Description Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /robot_description
      Enabled: true
      Links:
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        FL_calf:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FL_foot:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FL_hip:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FL_thigh:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FR_calf:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FR_foot:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FR_hip:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        FR_thigh:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        Link Tree Style: Links in Alphabetic Order
        RL_calf:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RL_foot:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RL_hip:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RL_thigh:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RR_calf:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RR_foot:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RR_hip:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        RR_thigh:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        base:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        camera_d435:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        front_camera:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        imu_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        lidar:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        trunk:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
      Mass Properties:
        Inertia: false
        Mass: false
      Name: RobotModel
      TF Prefix: ""
      Update Interval: 0
      Value: true
      Visual Enabled: true
    - Class: rviz_default_plugins/TF
      Enabled: true
      Filter (blacklist): ""
      Filter (whitelist): ""
      Frame Timeout: 15
      Frames:
        All Enabled: false
        FL_calf:
          Value: true
        FL_foot:
          Value: true
        FL_hip:
          Value: true
        FL_thigh:
          Value: true
        FR_calf:
          Value: true
        FR_foot:
          Value: true
        FR_hip:
          Value: true
        FR_thigh:
          Value: true
        RL_calf:
          Value: true
        RL_foot:
          Value: true
        RL_hip:
          Value: true
        RL_thigh:
          Value: true
        RR_calf:
          Value: true
        RR_foot:
          Value: true
        RR_hip:
          Value: true
        RR_thigh:
          Value: true
        base:
          Value: false
        camera_d435:
          Value: true
        front_camera:
          Value: true
        imu_link:
          Value: false
        lidar:
          Value: true
        odom:
          Value: true
        trunk:
          Value: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: false
      Show Names: false
      Tree:
        odom:
          base:
            trunk:
              FL_hip:
                FL_thigh:
                  FL_calf:
                    FL_foot:
                      {}
              FR_hip:
                FR_thigh:
                  FR_calf:
                    FR_foot:
                      {}
              RL_hip:
                RL_thigh:
                  RL_calf:
                    RL_foot:
                      {}
              RR_hip:
                RR_thigh:
                  RR_calf:
                    RR_foot:
                      {}
              camera_d435:
                {}
              front_camera:
                {}
              imu_link:
                {}
              lidar:
                {}
      Update Interval: 0
      Value: true
    - Class: rviz_default_plugins/MarkerArray
      Enabled: true
      Name: Optimized State Trajectory
      Namespaces:
        CoM Trajectory: true
        EE Trajectories: true
        Future footholds: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /legged_robot/optimizedStateTrajectory
      Value: true
    - Class: rviz_default_plugins/MarkerArray
      Enabled: true
      Name: Current State
      Namespaces:
        Center of Pressure: true
        EE Forces: true
        EE Positions: true
        Support Polygon: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /legged_robot/currentState
      Value: true
    - Class: rviz_common/Group
      Displays:
        - Class: rviz_common/Group
          Displays:
            - Class: rviz_default_plugins/Marker
              Enabled: true
              Name: Marker
              Namespaces:
                {}
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /legged_robot/desiredFeetTrajectory/LF
              Value: true
            - Class: rviz_default_plugins/Marker
              Enabled: true
              Name: Marker
              Namespaces:
                {}
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /legged_robot/desiredFeetTrajectory/LH
              Value: true
            - Class: rviz_default_plugins/Marker
              Enabled: true
              Name: Marker
              Namespaces:
                {}
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /legged_robot/desiredFeetTrajectory/RF
              Value: true
            - Class: rviz_default_plugins/Marker
              Enabled: true
              Name: Marker
              Namespaces:
                {}
              Topic:
                Depth: 5
                Durability Policy: Volatile
                Filter size: 10
                History Policy: Keep Last
                Reliability Policy: Reliable
                Value: /legged_robot/desiredFeetTrajectory/RH
              Value: true
          Enabled: false
          Name: Target Feet Trajectories
        - Class: rviz_default_plugins/Marker
          Enabled: true
          Name: Target Base Trajectory
          Namespaces:
            "": true
          Topic:
            Depth: 5
            Durability Policy: Volatile
            Filter size: 10
            History Policy: Keep Last
            Reliability Policy: Reliable
            Value: /legged_robot/desiredBaseTrajectory
          Value: true
      Enabled: true
      Name: Target Trajectories
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Class: grid_map_rviz_plugin/GridMap
      Color: 200; 200; 200
      Color Layer: elevation
      Color Transformer: GridMapLayer
      Enabled: true
      Height Layer: elevation
      Height Transformer: GridMapLayer
      History Length: 1
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 10
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: GridMap
      Show Grid Lines: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /raisim_heightmap
      Use Rainbow: true
      Value: true
    - Class: rviz_default_plugins/Image
      Enabled: true
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Image
      Normalize Range: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /camera/image
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz_default_plugins/PointCloud2
      Color: 255; 255; 255
      Color Transformer: RGB8
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: PointCloud2
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /rgbd_d435/points
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz_default_plugins/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 0
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: PointCloud2
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /scan/points
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
  Enabled: true
  Global Options:
    Background Color: 198; 198; 198
    Fixed Frame: odom
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz_default_plugins/Interact
      Hide Inactive Objects: true
    - Class: rviz_default_plugins/Select
    - Class: rviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: rviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /initialpose
    - Class: rviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /move_base_simple/goal
    - Class: rviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /clicked_point
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Class: rviz_default_plugins/Orbit
      Distance: 2.071281671524048
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: -1.0683034658432007
        Y: -0.7991123795509338
        Z: 0.4058437943458557
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.18039782345294952
      Target Frame: trunk
      Value: Orbit (rviz_default_plugins)
      Yaw: 3.5117714405059814
    Saved:
      - Class: rviz_default_plugins/Orbit
        Distance: 2.071281671524048
        Enable Stereo Rendering:
          Stereo Eye Separation: 0.05999999865889549
          Stereo Focal Distance: 1
          Swap Stereo Eyes: false
          Value: false
        Focal Point:
          X: -1.0683034658432007
          Y: -0.7991123795509338
          Z: 0.4058437943458557
        Focal Shape Fixed Size: true
        Focal Shape Size: 0.05000000074505806
        Invert Z Axis: false
        Name: Orbit
        Near Clip Distance: 0.009999999776482582
        Pitch: 0.46039777994155884
        Target Frame: trunk
        Value: Orbit (rviz_default_plugins)
        Yaw: 3.326773166656494
Window Geometry:
  Displays:
    collapsed: false
  Height: 1182
  Hide Left Dock: true
  Hide Right Dock: false
  Image:
    collapsed: false
  QMainWindow State: 000000ff00000000fd0000000400000000000001f4000003fefc020000000cfb0000001200530065006c0065006300740069006f006e000000006e000000b0000000b200fffffffb0000001e0054006f006f006c002000500072006f0070006500720074006900650073000000006e00000477000000b200fffffffb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c0061007900730000000070000003fe0000018600fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000000c00430061006d00650072006101000001be000001900000000000000000fb000000100044006900730070006c006100790073000000006e000002470000018600fffffffb0000000a0049006d0061006700650000000070000002990000002800fffffffb0000000a0049006d0061006700650100000395000000d90000000000000000000000010000015d000003fefc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a005600690065007700730000000070000003fe0000013800fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000006400000003efc0100000002fb0000000800540069006d00650000000000000006400000047a00fffffffb0000000800540069006d006501000000000000045000000000000000000000070f000003fe00000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1807
  X: 2248
  Y: 914
