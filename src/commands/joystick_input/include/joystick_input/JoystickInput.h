//
// Created by tlab-uav on 24-9-13.
//

#ifndef JOYSTICKINPUT_H
#define JOYSTICKINPUT_H

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/joy.hpp>
#include <control_input_msgs/msg/inputs.hpp>
#include <controller_manager_msgs/srv/switch_controller.hpp>
#include <map>
#include <string>

class JoystickInput final : public rclcpp::Node {
public:
    JoystickInput();

    ~JoystickInput() override = default;

private:
    // Joy回调函数
    void joy_callback(sensor_msgs::msg::Joy::SharedPtr msg);

    // 控制器切换函数
    void switch_controller(const std::string &target);
    void deactivate_current();

    // 控制输入消息
    control_input_msgs::msg::Inputs inputs_;

    // 发布器与订阅器
    rclcpp::Publisher<control_input_msgs::msg::Inputs>::SharedPtr publisher_;
    rclcpp::Subscription<sensor_msgs::msg::Joy>::SharedPtr subscription_;

    // 控制器切换服务客户端
    rclcpp::Client<controller_manager_msgs::srv::SwitchController>::SharedPtr switch_client_;

    // 控制器映射关系（command -> controller name）
    std::map<int, std::string> controller_map_;

    // 当前激活的控制器名称
    std::string current_controller_;

    // 上一次触发的 command 编号（用于防抖）
    int last_command_;
};

#endif //JOYSTICKINPUT_H
