//
// Created by tlab-uav on 24-9-13.
//

#include "joystick_input/JoystickInput.h"

using std::placeholders::_1;
using std::placeholders::_2;

JoystickInput::JoystickInput() : Node("joystick_input_node") {
    publisher_ = create_publisher<control_input_msgs::msg::Inputs>("control_input", 10);
    subscription_ = create_subscription<sensor_msgs::msg::Joy>(
        "joy", 10, std::bind(&JoystickInput::joy_callback, this, _1));

    switch_client_ = this->create_client<controller_manager_msgs::srv::SwitchController>(
        "/controller_manager/switch_controller");

    controller_map_ = {
        {9, "ocs2_quadruped_controller"},
        {7, "unitree_guide_controller"},
        {8, "rl_quadruped_controller"}
    };

    current_controller_ = "";
    last_command_ = 0;
}

void JoystickInput::joy_callback(sensor_msgs::msg::Joy::SharedPtr msg) {
    // 发布 control_input
    if (msg->buttons[1] && msg->buttons[4]) {
        inputs_.command = 1; // LB + B
        std::cout << "LB + B" << std::endl;
    } else if (msg->buttons[0] && msg->buttons[4]) {
        inputs_.command = 2; // LB + A
    } else if (msg->buttons[2] && msg->buttons[4]) {
        inputs_.command = 3; // LB + X
    } else if (msg->buttons[3] && msg->buttons[4]) {
        inputs_.command = 4; // LB + Y
    } else if (msg->axes[2] != 1 && msg->buttons[1]) {
        inputs_.command = 5; // LT + B
    } else if (msg->axes[2] != 1 && msg->buttons[0]) {
        inputs_.command = 6; // LT + A
    } else if (msg->axes[2] != 1 && msg->buttons[2]) {
        inputs_.command = 7; // LT + X
        std::cout << "LT + X" << std::endl;
    } else if (msg->axes[2] != 1 && msg->buttons[3]) {
        inputs_.command = 8; // LT + Y
    } else if (msg->buttons[7]) {
        inputs_.command = 9; // START
    } else if (msg->buttons[5] && msg->buttons[0]) { // LB + A
        inputs_.command = 101;
        // RCLCPP_ERROR(this->get_logger(), "LB + A");
        if (last_command_ != 101) {
            switch_controller("unitree_guide_controller");
            last_command_ = 101;
        }
    } else if (msg->buttons[5] && msg->buttons[3]) { // LB + Y
        inputs_.command = 102;
        if (last_command_ != 102) {
            switch_controller("ocs2_quadruped_controller");
            last_command_ = 102;
        }
    } else {
        inputs_.command = 0;
        inputs_.lx = -msg->axes[0];
        inputs_.ly = msg->axes[1];
        inputs_.rx = -msg->axes[3];
        inputs_.ry = msg->axes[4];
    }

    publisher_->publish(inputs_);

}

void JoystickInput::switch_controller(const std::string &target) {
    if (!switch_client_->wait_for_service(std::chrono::seconds(2))) {
        RCLCPP_ERROR(this->get_logger(), "SwitchController service not available");
        return;
    }

    auto request = std::make_shared<controller_manager_msgs::srv::SwitchController::Request>();
    request->activate_controllers = {target};
    if (!current_controller_.empty())
        request->deactivate_controllers = {current_controller_};
    request->strictness = controller_manager_msgs::srv::SwitchController::Request::STRICT;

    auto future = switch_client_->async_send_request(request,
        [this, target](rclcpp::Client<controller_manager_msgs::srv::SwitchController>::SharedFuture fut) {
            if (fut.get()->ok) {
                RCLCPP_INFO(this->get_logger(), "Switched to controller: %s", target.c_str());
                current_controller_ = target;
            } else {
                RCLCPP_ERROR(this->get_logger(), "Failed to switch controller to: %s", target.c_str());
            }
        });
}

void JoystickInput::deactivate_current() {
    if (current_controller_.empty()) return;

    auto request = std::make_shared<controller_manager_msgs::srv::SwitchController::Request>();
    request->deactivate_controllers = {current_controller_};
    request->strictness = controller_manager_msgs::srv::SwitchController::Request::STRICT;

    auto future = switch_client_->async_send_request(request,
        [this](rclcpp::Client<controller_manager_msgs::srv::SwitchController>::SharedFuture fut) {
            if (fut.get()->ok) {
                RCLCPP_INFO(this->get_logger(), "Deactivated controller: %s", current_controller_.c_str());
                current_controller_ = "";
            } else {
                RCLCPP_ERROR(this->get_logger(), "Failed to deactivate controller: %s", current_controller_.c_str());
            }
        });
}

int main(int argc, char *argv[]) {
    rclcpp::init(argc, argv);
    auto node = std::make_shared<JoystickInput>();
    spin(node);
    rclcpp::shutdown();
    return 0;}