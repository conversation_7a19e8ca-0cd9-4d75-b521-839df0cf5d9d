[0.000000] (-) TimerEvent: {}
[0.000348] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000548] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000612] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000682] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000728] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.001070] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.001138] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.001356] (-) JobUnselected: {'identifier': 'controller_common'}
[0.001394] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.001440] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.001477] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.001512] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.001553] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001849] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001927] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001943] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001958] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001973] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001988] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.002007] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.002020] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.002034] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.002047] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.002061] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.002074] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.002087] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.002101] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.002115] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.002128] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.002141] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.002154] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.002168] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.002181] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.002195] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.002208] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.002222] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.002235] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.002249] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.002261] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.002274] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.002288] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.002301] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.002315] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.002328] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.002341] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.002354] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.002368] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002381] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002394] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002407] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002419] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002483] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002497] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002510] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002524] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002537] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002550] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002564] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002578] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002591] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002605] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002632] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002646] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002660] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002673] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002687] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002700] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002713] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002727] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002741] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002754] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002767] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002780] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.002794] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.002807] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002824] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.002853] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.022607] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.023313] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/src/commands/joystick_input', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/install/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.098989] (-) TimerEvent: {}
[0.125199] (joystick_input) StdoutLine: {'line': b'-- The C compiler identification is GNU 13.3.0\n'}
[0.197649] (joystick_input) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 13.3.0\n'}
[0.199054] (-) TimerEvent: {}
[0.220149] (joystick_input) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.299169] (-) TimerEvent: {}
[0.326877] (joystick_input) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.338963] (joystick_input) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.339383] (joystick_input) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.339970] (joystick_input) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.353328] (joystick_input) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.399302] (-) TimerEvent: {}
[0.482790] (joystick_input) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.491178] (joystick_input) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.491457] (joystick_input) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.491784] (joystick_input) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.498856] (joystick_input) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.499352] (-) TimerEvent: {}
[0.599616] (-) TimerEvent: {}
[0.700152] (-) TimerEvent: {}
[0.736141] (joystick_input) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter \n'}
[0.800256] (-) TimerEvent: {}
[0.900564] (-) TimerEvent: {}
[0.948918] (joystick_input) StdoutLine: {'line': b'-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)\n'}
[1.000684] (-) TimerEvent: {}
[1.040429] (joystick_input) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)\n'}
[1.078091] (joystick_input) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)\n'}
[1.100800] (-) TimerEvent: {}
[1.119937] (joystick_input) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.152289] (joystick_input) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.200935] (-) TimerEvent: {}
[1.265064] (joystick_input) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)\n'}
[1.272774] (joystick_input) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)\n'}
[1.301049] (-) TimerEvent: {}
[1.401434] (-) TimerEvent: {}
[1.403990] (joystick_input) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  \n'}
[1.424097] (joystick_input) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") \n'}
[1.448025] (joystick_input) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.501576] (-) TimerEvent: {}
[1.546210] (joystick_input) StdoutLine: {'line': b'-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)\n'}
[1.601707] (-) TimerEvent: {}
[1.607845] (joystick_input) StdoutLine: {'line': b'-- Found control_input_msgs: 0.0.0 (/home/<USER>/install/control_input_msgs/share/control_input_msgs/cmake)\n'}
[1.621572] (joystick_input) StdoutLine: {'line': b'-- Found controller_manager_msgs: 4.32.0 (/opt/ros/jazzy/share/controller_manager_msgs/cmake)\n'}
[1.681656] (joystick_input) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[1.701787] (-) TimerEvent: {}
[1.767281] (joystick_input) StdoutLine: {'line': b'-- Configuring done (1.7s)\n'}
[1.782024] (joystick_input) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[1.786541] (joystick_input) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/build/joystick_input\n'}
[1.797201] (joystick_input) CommandEnded: {'returncode': 0}
[1.797823] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[1.798482] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[1.801854] (-) TimerEvent: {}
[1.873339] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[1.901990] (-) TimerEvent: {}
[2.002385] (-) TimerEvent: {}
[2.102897] (-) TimerEvent: {}
[2.203389] (-) TimerEvent: {}
[2.303859] (-) TimerEvent: {}
[2.404212] (-) TimerEvent: {}
[2.504593] (-) TimerEvent: {}
[2.605067] (-) TimerEvent: {}
[2.705538] (-) TimerEvent: {}
[2.805980] (-) TimerEvent: {}
[2.906314] (-) TimerEvent: {}
[3.006728] (-) TimerEvent: {}
[3.107154] (-) TimerEvent: {}
[3.207603] (-) TimerEvent: {}
[3.308175] (-) TimerEvent: {}
[3.408657] (-) TimerEvent: {}
[3.509041] (-) TimerEvent: {}
[3.609495] (-) TimerEvent: {}
[3.709846] (-) TimerEvent: {}
[3.810116] (-) TimerEvent: {}
[3.910434] (-) TimerEvent: {}
[4.010883] (-) TimerEvent: {}
[4.111343] (-) TimerEvent: {}
[4.211775] (-) TimerEvent: {}
[4.312160] (-) TimerEvent: {}
[4.412613] (-) TimerEvent: {}
[4.513123] (-) TimerEvent: {}
[4.613605] (-) TimerEvent: {}
[4.714024] (-) TimerEvent: {}
[4.814364] (-) TimerEvent: {}
[4.914687] (-) TimerEvent: {}
[5.015161] (-) TimerEvent: {}
[5.115934] (-) TimerEvent: {}
[5.216360] (-) TimerEvent: {}
[5.316743] (-) TimerEvent: {}
[5.417125] (-) TimerEvent: {}
[5.517528] (-) TimerEvent: {}
[5.617909] (-) TimerEvent: {}
[5.718292] (-) TimerEvent: {}
[5.818660] (-) TimerEvent: {}
[5.918992] (-) TimerEvent: {}
[6.019327] (-) TimerEvent: {}
[6.119661] (-) TimerEvent: {}
[6.220006] (-) TimerEvent: {}
[6.320341] (-) TimerEvent: {}
[6.420674] (-) TimerEvent: {}
[6.521004] (-) TimerEvent: {}
[6.621337] (-) TimerEvent: {}
[6.721655] (-) TimerEvent: {}
[6.821953] (-) TimerEvent: {}
[6.922266] (-) TimerEvent: {}
[7.022575] (-) TimerEvent: {}
[7.122893] (-) TimerEvent: {}
[7.223202] (-) TimerEvent: {}
[7.323518] (-) TimerEvent: {}
[7.423889] (-) TimerEvent: {}
[7.524388] (-) TimerEvent: {}
[7.624860] (-) TimerEvent: {}
[7.725258] (-) TimerEvent: {}
[7.825665] (-) TimerEvent: {}
[7.926153] (-) TimerEvent: {}
[8.026618] (-) TimerEvent: {}
[8.126992] (-) TimerEvent: {}
[8.227292] (-) TimerEvent: {}
[8.327610] (-) TimerEvent: {}
[8.427934] (-) TimerEvent: {}
[8.528250] (-) TimerEvent: {}
[8.628568] (-) TimerEvent: {}
[8.728885] (-) TimerEvent: {}
[8.829205] (-) TimerEvent: {}
[8.929524] (-) TimerEvent: {}
[9.029836] (-) TimerEvent: {}
[9.130157] (-) TimerEvent: {}
[9.230477] (-) TimerEvent: {}
[9.330787] (-) TimerEvent: {}
[9.431104] (-) TimerEvent: {}
[9.451025] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[9.531231] (-) TimerEvent: {}
[9.631568] (-) TimerEvent: {}
[9.704924] (joystick_input) StderrLine: {'line': b"/usr/bin/ld: /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o: in function `_start':\n"}
[9.705197] (joystick_input) StderrLine: {'line': b"(.text+0x1b): undefined reference to `main'\n"}
[9.731688] (-) TimerEvent: {}
[9.741469] (joystick_input) StderrLine: {'line': b'collect2: error: ld returned 1 exit status\n'}
[9.743559] (joystick_input) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/joystick_input.dir/build.make:208: joystick_input] Error 1\n'}
[9.744023] (joystick_input) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/joystick_input.dir/all] Error 2\n'}
[9.744258] (joystick_input) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[9.747352] (joystick_input) CommandEnded: {'returncode': 2}
[9.754744] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 2}
[9.765060] (-) EventReactorShutdown: {}
