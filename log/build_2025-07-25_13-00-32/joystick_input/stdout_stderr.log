-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found rclcpp: 28.1.10 (/opt/ros/jazzy/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.6.5 (/opt/ros/jazzy/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.3.2 (/opt/ros/jazzy/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 8.4.2 (/opt/ros/jazzy/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Found FastRTPS: /opt/ros/jazzy/include (Required is at least version "2.13") 
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found sensor_msgs: 5.3.6 (/opt/ros/jazzy/share/sensor_msgs/cmake)
-- Found control_input_msgs: 0.0.0 (/home/<USER>/install/control_input_msgs/share/control_input_msgs/cmake)
-- Found controller_manager_msgs: 4.32.0 (/opt/ros/jazzy/share/controller_manager_msgs/cmake)
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Configuring done (1.7s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/build/joystick_input
[ 50%] [32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o[0m
[100%] [32m[1mLinking CXX executable joystick_input[0m
/usr/bin/ld: /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o: in function `_start':
(.text+0x1b): undefined reference to `main'
collect2: error: ld returned 1 exit status
gmake[2]: *** [CMakeFiles/joystick_input.dir/build.make:208: joystick_input] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:137: CMakeFiles/joystick_input.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
