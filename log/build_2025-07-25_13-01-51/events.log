[0.000000] (-) TimerEvent: {}
[0.001620] (-) JobUnselected: {'identifier': 'a1_description'}
[0.001943] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.002092] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.002118] (-) JobUnselected: {'identifier': 'b2_description'}
[0.002136] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.002154] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.002171] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.002186] (-) JobUnselected: {'identifier': 'controller_common'}
[0.002203] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.002218] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.002234] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.002249] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.002266] (-) JobUnselected: {'identifier': 'go1_description'}
[0.002281] (-) JobUnselected: {'identifier': 'go2_description'}
[0.002296] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.002312] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.002327] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.002341] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.002357] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.002386] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.002403] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.002423] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.002437] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.002451] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.002468] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.002483] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.002500] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.002514] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.002531] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.002545] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.002569] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.002592] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.002608] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.002623] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.002638] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.002653] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.002667] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.002683] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.002698] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.002713] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.002729] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.002745] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.002760] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.002774] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.002790] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.002805] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.002820] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002834] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002848] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002864] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002879] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002894] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002909] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002925] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002939] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002953] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002969] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002984] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002999] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.003014] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.003027] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.003070] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.003086] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.003101] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.003117] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.003132] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.003148] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.003163] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.003178] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.003194] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.003207] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003222] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003237] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003251] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.003266] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003287] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.003322] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.025960] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.027170] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[0.028040] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.099448] (-) TimerEvent: {}
[0.136124] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[0.199607] (-) TimerEvent: {}
[0.300178] (-) TimerEvent: {}
[0.400650] (-) TimerEvent: {}
[0.501166] (-) TimerEvent: {}
[0.601864] (-) TimerEvent: {}
[0.702517] (-) TimerEvent: {}
[0.803174] (-) TimerEvent: {}
[0.903790] (-) TimerEvent: {}
[1.004294] (-) TimerEvent: {}
[1.104934] (-) TimerEvent: {}
[1.205543] (-) TimerEvent: {}
[1.306170] (-) TimerEvent: {}
[1.406782] (-) TimerEvent: {}
[1.507396] (-) TimerEvent: {}
[1.608043] (-) TimerEvent: {}
[1.708823] (-) TimerEvent: {}
[1.809393] (-) TimerEvent: {}
[1.909855] (-) TimerEvent: {}
[2.010502] (-) TimerEvent: {}
[2.111156] (-) TimerEvent: {}
[2.211829] (-) TimerEvent: {}
[2.312506] (-) TimerEvent: {}
[2.413107] (-) TimerEvent: {}
[2.513635] (-) TimerEvent: {}
[2.614150] (-) TimerEvent: {}
[2.714652] (-) TimerEvent: {}
[2.815156] (-) TimerEvent: {}
[2.915639] (-) TimerEvent: {}
[3.016174] (-) TimerEvent: {}
[3.116695] (-) TimerEvent: {}
[3.217193] (-) TimerEvent: {}
[3.317701] (-) TimerEvent: {}
[3.418226] (-) TimerEvent: {}
[3.518753] (-) TimerEvent: {}
[3.619264] (-) TimerEvent: {}
[3.719774] (-) TimerEvent: {}
[3.820260] (-) TimerEvent: {}
[3.920643] (-) TimerEvent: {}
[4.021043] (-) TimerEvent: {}
[4.121585] (-) TimerEvent: {}
[4.222110] (-) TimerEvent: {}
[4.322631] (-) TimerEvent: {}
[4.423150] (-) TimerEvent: {}
[4.523677] (-) TimerEvent: {}
[4.624205] (-) TimerEvent: {}
[4.724769] (-) TimerEvent: {}
[4.825293] (-) TimerEvent: {}
[4.925830] (-) TimerEvent: {}
[5.026352] (-) TimerEvent: {}
[5.126870] (-) TimerEvent: {}
[5.227349] (-) TimerEvent: {}
[5.327690] (-) TimerEvent: {}
[5.428151] (-) TimerEvent: {}
[5.528614] (-) TimerEvent: {}
[5.628968] (-) TimerEvent: {}
[5.729354] (-) TimerEvent: {}
[5.829716] (-) TimerEvent: {}
[5.930069] (-) TimerEvent: {}
[6.030429] (-) TimerEvent: {}
[6.130784] (-) TimerEvent: {}
[6.231135] (-) TimerEvent: {}
[6.331459] (-) TimerEvent: {}
[6.431843] (-) TimerEvent: {}
[6.532173] (-) TimerEvent: {}
[6.632593] (-) TimerEvent: {}
[6.733113] (-) TimerEvent: {}
[6.833679] (-) TimerEvent: {}
[6.934221] (-) TimerEvent: {}
[7.034839] (-) TimerEvent: {}
[7.135352] (-) TimerEvent: {}
[7.235917] (-) TimerEvent: {}
[7.336423] (-) TimerEvent: {}
[7.436946] (-) TimerEvent: {}
[7.537456] (-) TimerEvent: {}
[7.637930] (-) TimerEvent: {}
[7.738259] (-) TimerEvent: {}
[7.838646] (-) TimerEvent: {}
[7.939110] (-) TimerEvent: {}
[8.039606] (-) TimerEvent: {}
[8.041305] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[8.139743] (-) TimerEvent: {}
[8.240263] (-) TimerEvent: {}
[8.340786] (-) TimerEvent: {}
[8.441309] (-) TimerEvent: {}
[8.454984] (joystick_input) StdoutLine: {'line': b'[100%] Built target joystick_input\n'}
[8.469842] (joystick_input) CommandEnded: {'returncode': 0}
[8.470640] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'install'}
[8.482527] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/build/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.494018] (joystick_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.494249] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input\n'}
[8.498509] (joystick_input) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""\n'}
[8.498674] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input//launch\n'}
[8.498780] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py\n'}
[8.498900] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input\n'}
[8.499039] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input\n'}
[8.499172] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh\n'}
[8.499306] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv\n'}
[8.499419] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh\n'}
[8.499542] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv\n'}
[8.499654] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash\n'}
[8.499763] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh\n'}
[8.499877] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh\n'}
[8.500085] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv\n'}
[8.500186] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv\n'}
[8.500282] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input\n'}
[8.500382] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake\n'}
[8.500490] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake\n'}
[8.500588] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.xml\n'}
[8.501901] (joystick_input) CommandEnded: {'returncode': 0}
[8.523900] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 0}
[8.524752] (-) EventReactorShutdown: {}
