[0.000000] (-) TimerEvent: {}
[0.000335] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000461] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000906] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.001086] (-) JobUnselected: {'identifier': 'b2_description'}
[0.001131] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.001213] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.001231] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.001247] (-) JobUnselected: {'identifier': 'controller_common'}
[0.001281] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.001299] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.001325] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.001341] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.001589] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001617] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001690] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001906] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001927] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001939] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001952] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.001968] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001979] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001988] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001999] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.002008] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.002019] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.002031] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.002042] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.002053] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.002063] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.002088] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.002101] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.002112] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.002122] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.002131] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.002317] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.002344] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.002355] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.002365] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.002374] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.002387] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.002411] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.002421] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.002432] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.002442] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.002453] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.002477] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.002487] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002498] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002508] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002525] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002538] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002548] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002559] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002569] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002579] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002590] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002601] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002613] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002623] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002649] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002662] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002687] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002702] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002716] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002730] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002744] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002759] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002775] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002790] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002803] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002914] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003008] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003025] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003035] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.003044] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003059] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.003088] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.017977] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.018489] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[0.019074] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.099613] (-) TimerEvent: {}
[0.114678] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[0.199725] (-) TimerEvent: {}
[0.300062] (-) TimerEvent: {}
[0.400413] (-) TimerEvent: {}
[0.500749] (-) TimerEvent: {}
[0.601089] (-) TimerEvent: {}
[0.701421] (-) TimerEvent: {}
[0.801739] (-) TimerEvent: {}
[0.902025] (-) TimerEvent: {}
[1.002322] (-) TimerEvent: {}
[1.102659] (-) TimerEvent: {}
[1.202994] (-) TimerEvent: {}
[1.303331] (-) TimerEvent: {}
[1.405101] (-) TimerEvent: {}
[1.505412] (-) TimerEvent: {}
[1.605721] (-) TimerEvent: {}
[1.706033] (-) TimerEvent: {}
[1.806342] (-) TimerEvent: {}
[1.906667] (-) TimerEvent: {}
[2.006967] (-) TimerEvent: {}
[2.107265] (-) TimerEvent: {}
[2.207552] (-) TimerEvent: {}
[2.307862] (-) TimerEvent: {}
[2.409115] (-) TimerEvent: {}
[2.509477] (-) TimerEvent: {}
[2.609795] (-) TimerEvent: {}
[2.710114] (-) TimerEvent: {}
[2.810457] (-) TimerEvent: {}
[2.910761] (-) TimerEvent: {}
[3.011054] (-) TimerEvent: {}
[3.111340] (-) TimerEvent: {}
[3.211652] (-) TimerEvent: {}
[3.311986] (-) TimerEvent: {}
[3.412301] (-) TimerEvent: {}
[3.512604] (-) TimerEvent: {}
[3.612915] (-) TimerEvent: {}
[3.713234] (-) TimerEvent: {}
[3.813549] (-) TimerEvent: {}
[3.913850] (-) TimerEvent: {}
[4.014140] (-) TimerEvent: {}
[4.114461] (-) TimerEvent: {}
[4.214768] (-) TimerEvent: {}
[4.315067] (-) TimerEvent: {}
[4.415358] (-) TimerEvent: {}
[4.515643] (-) TimerEvent: {}
[4.615935] (-) TimerEvent: {}
[4.716292] (-) TimerEvent: {}
[4.816606] (-) TimerEvent: {}
[4.916918] (-) TimerEvent: {}
[5.017234] (-) TimerEvent: {}
[5.117537] (-) TimerEvent: {}
[5.217867] (-) TimerEvent: {}
[5.318374] (-) TimerEvent: {}
[5.418683] (-) TimerEvent: {}
[5.518990] (-) TimerEvent: {}
[5.619337] (-) TimerEvent: {}
[5.719685] (-) TimerEvent: {}
[5.819999] (-) TimerEvent: {}
[5.920302] (-) TimerEvent: {}
[6.020624] (-) TimerEvent: {}
[6.120919] (-) TimerEvent: {}
[6.221210] (-) TimerEvent: {}
[6.321492] (-) TimerEvent: {}
[6.425408] (-) TimerEvent: {}
[6.525713] (-) TimerEvent: {}
[6.626036] (-) TimerEvent: {}
[6.726349] (-) TimerEvent: {}
[6.826638] (-) TimerEvent: {}
[6.926926] (-) TimerEvent: {}
[7.027233] (-) TimerEvent: {}
[7.127559] (-) TimerEvent: {}
[7.227873] (-) TimerEvent: {}
[7.328183] (-) TimerEvent: {}
[7.428475] (-) TimerEvent: {}
[7.528782] (-) TimerEvent: {}
[7.629089] (-) TimerEvent: {}
[7.729405] (-) TimerEvent: {}
[7.829736] (-) TimerEvent: {}
[7.930052] (-) TimerEvent: {}
[8.030337] (-) TimerEvent: {}
[8.130628] (-) TimerEvent: {}
[8.230918] (-) TimerEvent: {}
[8.331279] (-) TimerEvent: {}
[8.431590] (-) TimerEvent: {}
[8.531914] (-) TimerEvent: {}
[8.632214] (-) TimerEvent: {}
[8.706189] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[8.732326] (-) TimerEvent: {}
[8.832629] (-) TimerEvent: {}
[8.932942] (-) TimerEvent: {}
[9.033273] (-) TimerEvent: {}
[9.066560] (joystick_input) StdoutLine: {'line': b'[100%] Built target joystick_input\n'}
[9.079996] (joystick_input) CommandEnded: {'returncode': 0}
[9.080767] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'install'}
[9.091798] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/build/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[9.101340] (joystick_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[9.101636] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input\n'}
[9.107896] (joystick_input) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""\n'}
[9.108232] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch\n'}
[9.108407] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py\n'}
[9.108515] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input\n'}
[9.108582] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input\n'}
[9.108633] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh\n'}
[9.108682] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv\n'}
[9.108730] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh\n'}
[9.108775] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv\n'}
[9.108823] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash\n'}
[9.108869] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh\n'}
[9.108915] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh\n'}
[9.108963] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv\n'}
[9.109009] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv\n'}
[9.109082] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input\n'}
[9.109164] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake\n'}
[9.109220] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake\n'}
[9.109292] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml\n'}
[9.110507] (joystick_input) CommandEnded: {'returncode': 0}
[9.133569] (-) TimerEvent: {}
[9.142846] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 0}
[9.143650] (-) EventReactorShutdown: {}
