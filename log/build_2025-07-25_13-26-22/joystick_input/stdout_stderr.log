[ 50%] [32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o[0m
[100%] [32m[1mLinking CXX executable joystick_input[0m
[100%] Built target joystick_input
-- Install configuration: ""
-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input
-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py
-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input
-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv
-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv
-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake
-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml
