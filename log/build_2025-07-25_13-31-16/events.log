[0.000000] (-) TimerEvent: {}
[0.000586] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000730] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000767] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000794] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000821] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000846] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000870] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000894] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000918] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000942] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.000966] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.000989] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.001012] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001035] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001058] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001081] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001105] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001127] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001151] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.001184] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001208] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001231] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001253] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001276] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.001299] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001322] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.001345] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001369] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001402] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001426] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001449] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001471] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001495] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001517] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001540] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001563] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001586] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001609] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001632] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001655] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001678] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001700] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001723] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001747] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001769] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001793] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001817] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.001840] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.001863] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.001886] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.001908] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.001931] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.001954] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.001976] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.001999] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002022] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002045] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002069] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002092] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002115] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002138] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002287] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002312] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002335] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002358] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002382] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002405] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002428] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002451] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002474] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002497] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002519] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002542] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.002564] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.002587] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002615] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.002658] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.028492] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.029139] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[0.029826] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.098657] (-) TimerEvent: {}
[0.136619] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[0.198826] (-) TimerEvent: {}
[0.299388] (-) TimerEvent: {}
[0.399793] (-) TimerEvent: {}
[0.500192] (-) TimerEvent: {}
[0.600636] (-) TimerEvent: {}
[0.701201] (-) TimerEvent: {}
[0.801701] (-) TimerEvent: {}
[0.902180] (-) TimerEvent: {}
[1.002771] (-) TimerEvent: {}
[1.103387] (-) TimerEvent: {}
[1.203984] (-) TimerEvent: {}
[1.304572] (-) TimerEvent: {}
[1.405168] (-) TimerEvent: {}
[1.505775] (-) TimerEvent: {}
[1.606381] (-) TimerEvent: {}
[1.707048] (-) TimerEvent: {}
[1.807473] (-) TimerEvent: {}
[1.907963] (-) TimerEvent: {}
[2.008405] (-) TimerEvent: {}
[2.108848] (-) TimerEvent: {}
[2.209355] (-) TimerEvent: {}
[2.309937] (-) TimerEvent: {}
[2.410548] (-) TimerEvent: {}
[2.511134] (-) TimerEvent: {}
[2.611646] (-) TimerEvent: {}
[2.712130] (-) TimerEvent: {}
[2.812631] (-) TimerEvent: {}
[2.913016] (-) TimerEvent: {}
[3.013498] (-) TimerEvent: {}
[3.114076] (-) TimerEvent: {}
[3.214572] (-) TimerEvent: {}
[3.314933] (-) TimerEvent: {}
[3.415460] (-) TimerEvent: {}
[3.515968] (-) TimerEvent: {}
[3.616470] (-) TimerEvent: {}
[3.716940] (-) TimerEvent: {}
[3.817249] (-) TimerEvent: {}
[3.917646] (-) TimerEvent: {}
[4.018150] (-) TimerEvent: {}
[4.118664] (-) TimerEvent: {}
[4.219181] (-) TimerEvent: {}
[4.319686] (-) TimerEvent: {}
[4.420208] (-) TimerEvent: {}
[4.520741] (-) TimerEvent: {}
[4.621235] (-) TimerEvent: {}
[4.721659] (-) TimerEvent: {}
[4.822188] (-) TimerEvent: {}
[4.922705] (-) TimerEvent: {}
[5.023205] (-) TimerEvent: {}
[5.123680] (-) TimerEvent: {}
[5.224086] (-) TimerEvent: {}
[5.324606] (-) TimerEvent: {}
[5.425115] (-) TimerEvent: {}
[5.525610] (-) TimerEvent: {}
[5.626118] (-) TimerEvent: {}
[5.726624] (-) TimerEvent: {}
[5.827122] (-) TimerEvent: {}
[5.927575] (-) TimerEvent: {}
[6.028073] (-) TimerEvent: {}
[6.128543] (-) TimerEvent: {}
[6.229029] (-) TimerEvent: {}
[6.329530] (-) TimerEvent: {}
[6.430005] (-) TimerEvent: {}
[6.530433] (-) TimerEvent: {}
[6.630917] (-) TimerEvent: {}
[6.731419] (-) TimerEvent: {}
[6.831952] (-) TimerEvent: {}
[6.932455] (-) TimerEvent: {}
[7.032936] (-) TimerEvent: {}
[7.133397] (-) TimerEvent: {}
[7.234045] (-) TimerEvent: {}
[7.334582] (-) TimerEvent: {}
[7.435104] (-) TimerEvent: {}
[7.535441] (-) TimerEvent: {}
[7.635894] (-) TimerEvent: {}
[7.736379] (-) TimerEvent: {}
[7.836876] (-) TimerEvent: {}
[7.937350] (-) TimerEvent: {}
[8.037746] (-) TimerEvent: {}
[8.076273] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[8.137868] (-) TimerEvent: {}
[8.238321] (-) TimerEvent: {}
[8.338817] (-) TimerEvent: {}
[8.382400] (joystick_input) StdoutLine: {'line': b'[100%] Built target joystick_input\n'}
[8.398354] (joystick_input) CommandEnded: {'returncode': 0}
[8.399103] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'install'}
[8.410632] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/build/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.424768] (joystick_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.425017] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input\n'}
[8.431544] (joystick_input) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""\n'}
[8.431750] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch\n'}
[8.431950] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py\n'}
[8.432156] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input\n'}
[8.432315] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input\n'}
[8.432497] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh\n'}
[8.432634] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv\n'}
[8.432750] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh\n'}
[8.432861] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv\n'}
[8.432974] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash\n'}
[8.433086] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh\n'}
[8.433196] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh\n'}
[8.433301] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv\n'}
[8.433460] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv\n'}
[8.433680] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input\n'}
[8.434108] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake\n'}
[8.434550] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake\n'}
[8.434644] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml\n'}
[8.435044] (joystick_input) CommandEnded: {'returncode': 0}
[8.438976] (-) TimerEvent: {}
[8.449952] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 0}
[8.451102] (-) EventReactorShutdown: {}
