Invoking command in '/home/<USER>/build/joystick_input': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/build/joystick_input -- -j12 -l12
Invoked command in '/home/<USER>/build/joystick_input' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/build/joystick_input -- -j12 -l12
Invoking command in '/home/<USER>/build/joystick_input': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/build/joystick_input
Invoked command in '/home/<USER>/build/joystick_input' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/build/joystick_input
