[0.000000] (-) TimerEvent: {}
[0.000680] (-) JobUnselected: {'identifier': 'a1_description'}
[0.001022] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.001156] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.001191] (-) JobUnselected: {'identifier': 'b2_description'}
[0.001237] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.001267] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.001294] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.001320] (-) JobUnselected: {'identifier': 'controller_common'}
[0.001345] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.001371] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.001396] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.001422] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.001448] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001739] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001767] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001792] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001817] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001841] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001866] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.001915] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001941] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001967] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001992] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.002017] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.002041] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.002068] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.002094] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.002119] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.002144] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.002168] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.002192] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.002216] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.002243] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.002268] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.002291] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.002317] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.002341] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.002366] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.002391] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.002416] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.002441] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.002471] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.002494] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.002520] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.002544] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.002559] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.002749] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002766] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002782] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002796] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002811] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002826] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002840] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002855] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002870] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002884] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002900] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002915] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002933] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002953] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002971] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.003191] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.003230] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.003250] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.003269] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.003287] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.003307] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.003327] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.003343] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.003357] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.003371] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003389] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003403] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003417] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.003431] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003449] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.003479] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.023226] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.023854] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[0.024663] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.099313] (-) TimerEvent: {}
[0.111608] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[0.199470] (-) TimerEvent: {}
[0.300005] (-) TimerEvent: {}
[0.400479] (-) TimerEvent: {}
[0.500814] (-) TimerEvent: {}
[0.601196] (-) TimerEvent: {}
[0.701730] (-) TimerEvent: {}
[0.802261] (-) TimerEvent: {}
[0.902764] (-) TimerEvent: {}
[1.003266] (-) TimerEvent: {}
[1.103674] (-) TimerEvent: {}
[1.204041] (-) TimerEvent: {}
[1.304517] (-) TimerEvent: {}
[1.404891] (-) TimerEvent: {}
[1.505400] (-) TimerEvent: {}
[1.605948] (-) TimerEvent: {}
[1.706431] (-) TimerEvent: {}
[1.806806] (-) TimerEvent: {}
[1.907296] (-) TimerEvent: {}
[2.007683] (-) TimerEvent: {}
[2.108077] (-) TimerEvent: {}
[2.208422] (-) TimerEvent: {}
[2.308788] (-) TimerEvent: {}
[2.409180] (-) TimerEvent: {}
[2.509642] (-) TimerEvent: {}
[2.610151] (-) TimerEvent: {}
[2.710668] (-) TimerEvent: {}
[2.811164] (-) TimerEvent: {}
[2.911619] (-) TimerEvent: {}
[3.012025] (-) TimerEvent: {}
[3.112510] (-) TimerEvent: {}
[3.212869] (-) TimerEvent: {}
[3.313363] (-) TimerEvent: {}
[3.413776] (-) TimerEvent: {}
[3.514199] (-) TimerEvent: {}
[3.614624] (-) TimerEvent: {}
[3.715126] (-) TimerEvent: {}
[3.815586] (-) TimerEvent: {}
[3.916059] (-) TimerEvent: {}
[4.016552] (-) TimerEvent: {}
[4.117039] (-) TimerEvent: {}
[4.217479] (-) TimerEvent: {}
[4.317982] (-) TimerEvent: {}
[4.418430] (-) TimerEvent: {}
[4.518895] (-) TimerEvent: {}
[4.619305] (-) TimerEvent: {}
[4.719818] (-) TimerEvent: {}
[4.820331] (-) TimerEvent: {}
[4.920838] (-) TimerEvent: {}
[5.021341] (-) TimerEvent: {}
[5.121834] (-) TimerEvent: {}
[5.222281] (-) TimerEvent: {}
[5.322647] (-) TimerEvent: {}
[5.423140] (-) TimerEvent: {}
[5.523610] (-) TimerEvent: {}
[5.624023] (-) TimerEvent: {}
[5.724529] (-) TimerEvent: {}
[5.825005] (-) TimerEvent: {}
[5.925358] (-) TimerEvent: {}
[6.025737] (-) TimerEvent: {}
[6.126213] (-) TimerEvent: {}
[6.226587] (-) TimerEvent: {}
[6.327098] (-) TimerEvent: {}
[6.427601] (-) TimerEvent: {}
[6.528166] (-) TimerEvent: {}
[6.628657] (-) TimerEvent: {}
[6.729156] (-) TimerEvent: {}
[6.829663] (-) TimerEvent: {}
[6.930167] (-) TimerEvent: {}
[7.030634] (-) TimerEvent: {}
[7.131017] (-) TimerEvent: {}
[7.231379] (-) TimerEvent: {}
[7.331918] (-) TimerEvent: {}
[7.432442] (-) TimerEvent: {}
[7.532908] (-) TimerEvent: {}
[7.633314] (-) TimerEvent: {}
[7.733847] (-) TimerEvent: {}
[7.769137] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[7.833986] (-) TimerEvent: {}
[7.934483] (-) TimerEvent: {}
[8.034948] (-) TimerEvent: {}
[8.135342] (-) TimerEvent: {}
[8.181148] (joystick_input) StdoutLine: {'line': b'[100%] Built target joystick_input\n'}
[8.193522] (joystick_input) CommandEnded: {'returncode': 0}
[8.194393] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'install'}
[8.205831] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/build/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.218274] (joystick_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.218595] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input\n'}
[8.225087] (joystick_input) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""\n'}
[8.225370] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch\n'}
[8.225616] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py\n'}
[8.225791] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input\n'}
[8.225973] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input\n'}
[8.226106] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh\n'}
[8.226240] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv\n'}
[8.226369] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh\n'}
[8.226497] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv\n'}
[8.226628] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash\n'}
[8.226753] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh\n'}
[8.226914] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh\n'}
[8.227324] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv\n'}
[8.227554] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv\n'}
[8.227683] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input\n'}
[8.228215] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake\n'}
[8.228340] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake\n'}
[8.228416] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml\n'}
[8.228496] (joystick_input) CommandEnded: {'returncode': 0}
[8.235489] (-) TimerEvent: {}
[8.241504] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 0}
[8.242521] (-) EventReactorShutdown: {}
