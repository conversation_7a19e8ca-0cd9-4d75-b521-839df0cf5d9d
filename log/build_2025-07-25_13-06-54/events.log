[0.000000] (-) TimerEvent: {}
[0.000983] (-) JobUnselected: {'identifier': 'a1_description'}
[0.001216] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.001307] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.001361] (-) JobUnselected: {'identifier': 'b2_description'}
[0.001729] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.001777] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.002026] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.002084] (-) JobUnselected: {'identifier': 'controller_common'}
[0.002342] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.002472] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.002584] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.002698] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.002752] (-) JobUnselected: {'identifier': 'go1_description'}
[0.002873] (-) JobUnselected: {'identifier': 'go2_description'}
[0.002912] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.002955] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.002993] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.003055] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.003116] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.003216] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.003263] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.003317] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.003432] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.003478] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.003521] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.003630] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.003673] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.003911] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.003989] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.004055] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.004122] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.004227] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.004249] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.004265] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.004280] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.004297] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.004313] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.004329] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.004345] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.004360] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.004378] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.004393] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.004409] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.004424] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.004439] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.004454] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.004469] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.004485] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.004500] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.004516] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.004531] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.004547] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.004563] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.004577] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.004594] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.004609] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.004626] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.004641] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.004658] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.004673] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.004688] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.004716] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.004732] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.004747] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.004764] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.004779] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.004793] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.004809] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.004824] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.004936] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.004962] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.004979] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.004994] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.005010] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.005025] (-) JobUnselected: {'identifier': 'x30_description'}
[0.005048] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.005085] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.024505] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.025244] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[0.026176] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.098903] (-) TimerEvent: {}
[0.132810] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[0.199078] (-) TimerEvent: {}
[0.299639] (-) TimerEvent: {}
[0.400186] (-) TimerEvent: {}
[0.500696] (-) TimerEvent: {}
[0.601230] (-) TimerEvent: {}
[0.701797] (-) TimerEvent: {}
[0.802348] (-) TimerEvent: {}
[0.902947] (-) TimerEvent: {}
[1.003549] (-) TimerEvent: {}
[1.103974] (-) TimerEvent: {}
[1.204514] (-) TimerEvent: {}
[1.305008] (-) TimerEvent: {}
[1.405467] (-) TimerEvent: {}
[1.505871] (-) TimerEvent: {}
[1.606312] (-) TimerEvent: {}
[1.706868] (-) TimerEvent: {}
[1.807328] (-) TimerEvent: {}
[1.907875] (-) TimerEvent: {}
[2.008322] (-) TimerEvent: {}
[2.108890] (-) TimerEvent: {}
[2.209343] (-) TimerEvent: {}
[2.309913] (-) TimerEvent: {}
[2.410336] (-) TimerEvent: {}
[2.510762] (-) TimerEvent: {}
[2.611135] (-) TimerEvent: {}
[2.711622] (-) TimerEvent: {}
[2.812009] (-) TimerEvent: {}
[2.912468] (-) TimerEvent: {}
[3.012859] (-) TimerEvent: {}
[3.113369] (-) TimerEvent: {}
[3.213794] (-) TimerEvent: {}
[3.314280] (-) TimerEvent: {}
[3.414669] (-) TimerEvent: {}
[3.515162] (-) TimerEvent: {}
[3.615586] (-) TimerEvent: {}
[3.716067] (-) TimerEvent: {}
[3.816516] (-) TimerEvent: {}
[3.917127] (-) TimerEvent: {}
[4.017546] (-) TimerEvent: {}
[4.118024] (-) TimerEvent: {}
[4.218434] (-) TimerEvent: {}
[4.318937] (-) TimerEvent: {}
[4.419417] (-) TimerEvent: {}
[4.519891] (-) TimerEvent: {}
[4.620263] (-) TimerEvent: {}
[4.720743] (-) TimerEvent: {}
[4.821217] (-) TimerEvent: {}
[4.921700] (-) TimerEvent: {}
[5.022158] (-) TimerEvent: {}
[5.122691] (-) TimerEvent: {}
[5.223246] (-) TimerEvent: {}
[5.323762] (-) TimerEvent: {}
[5.424263] (-) TimerEvent: {}
[5.524764] (-) TimerEvent: {}
[5.625269] (-) TimerEvent: {}
[5.725773] (-) TimerEvent: {}
[5.826217] (-) TimerEvent: {}
[5.926664] (-) TimerEvent: {}
[6.027044] (-) TimerEvent: {}
[6.127533] (-) TimerEvent: {}
[6.228015] (-) TimerEvent: {}
[6.328495] (-) TimerEvent: {}
[6.428967] (-) TimerEvent: {}
[6.529465] (-) TimerEvent: {}
[6.629953] (-) TimerEvent: {}
[6.730435] (-) TimerEvent: {}
[6.830892] (-) TimerEvent: {}
[6.931385] (-) TimerEvent: {}
[7.031858] (-) TimerEvent: {}
[7.132339] (-) TimerEvent: {}
[7.232798] (-) TimerEvent: {}
[7.333342] (-) TimerEvent: {}
[7.433681] (-) TimerEvent: {}
[7.534150] (-) TimerEvent: {}
[7.634591] (-) TimerEvent: {}
[7.734955] (-) TimerEvent: {}
[7.835462] (-) TimerEvent: {}
[7.935928] (-) TimerEvent: {}
[8.036320] (-) TimerEvent: {}
[8.038252] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[8.136467] (-) TimerEvent: {}
[8.237004] (-) TimerEvent: {}
[8.337512] (-) TimerEvent: {}
[8.438027] (-) TimerEvent: {}
[8.450788] (joystick_input) StdoutLine: {'line': b'[100%] Built target joystick_input\n'}
[8.466373] (joystick_input) CommandEnded: {'returncode': 0}
[8.467789] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'install'}
[8.478894] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/build/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.493006] (joystick_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.493232] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input\n'}
[8.499393] (joystick_input) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""\n'}
[8.499537] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch\n'}
[8.499678] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py\n'}
[8.499819] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input\n'}
[8.500039] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input\n'}
[8.500248] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh\n'}
[8.500371] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv\n'}
[8.500484] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh\n'}
[8.500596] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv\n'}
[8.500704] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash\n'}
[8.500809] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh\n'}
[8.501004] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh\n'}
[8.501146] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv\n'}
[8.501266] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv\n'}
[8.501383] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input\n'}
[8.501597] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake\n'}
[8.501725] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake\n'}
[8.501844] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml\n'}
[8.502568] (joystick_input) CommandEnded: {'returncode': 0}
[8.520774] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 0}
[8.522354] (-) EventReactorShutdown: {}
