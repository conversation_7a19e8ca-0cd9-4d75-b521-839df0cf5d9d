[0.027s] Invoking command in '/home/<USER>/build/joystick_input': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/build/joystick_input -- -j12 -l12
[0.139s] [ 50%] [32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o[0m
[8.298s] [100%] [32m[1mLinking CXX executable joystick_input[0m
[8.612s] [100%] Built target joystick_input
[8.623s] Invoked command in '/home/<USER>/build/joystick_input' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/build/joystick_input -- -j12 -l12
[8.639s] Invoking command in '/home/<USER>/build/joystick_input': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/build/joystick_input
[8.650s] -- Install configuration: ""
[8.650s] -- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input
[8.654s] -- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv
[8.655s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv
[8.656s] -- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake
[8.656s] -- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml
[8.657s] Invoked command in '/home/<USER>/build/joystick_input' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy LD_LIBRARY_PATH=/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/build/joystick_input
