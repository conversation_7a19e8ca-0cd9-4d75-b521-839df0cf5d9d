[0.000000] (-) TimerEvent: {}
[0.000617] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000835] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000903] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000957] (-) JobUnselected: {'identifier': 'b2_description'}
[0.001618] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.001666] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.001705] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.001743] (-) JobUnselected: {'identifier': 'controller_common'}
[0.001782] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.001821] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.001861] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.001898] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.001934] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001971] (-) JobUnselected: {'identifier': 'go2_description'}
[0.002010] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.002046] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.002084] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.002124] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.002163] (-) JobUnselected: {'identifier': 'hardware_unitree_sdk2'}
[0.002208] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.002245] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.002281] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.002301] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.002317] (-) JobUnselected: {'identifier': 'magicdog_description'}
[0.002336] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.002353] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.002371] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.002386] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.002400] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.002417] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.002431] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.002446] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.002462] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.002477] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.002493] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.002508] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.002522] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.002538] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.002553] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.002569] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.002584] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.002599] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.002614] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.002629] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.002645] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.002660] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.002674] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002692] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002706] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002722] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002737] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002752] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002768] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002783] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002797] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002813] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002830] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002848] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002863] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002879] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002894] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.003216] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.003409] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.003535] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.003559] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.003574] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.003589] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.003606] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.003621] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.003636] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.003704] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.003721] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.003738] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.003752] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.003768] (-) JobUnselected: {'identifier': 'x30_description'}
[0.003790] (joystick_input) JobQueued: {'identifier': 'joystick_input', 'dependencies': OrderedDict({'control_input_msgs': '/home/<USER>/install/control_input_msgs'})}
[0.003824] (joystick_input) JobStarted: {'identifier': 'joystick_input'}
[0.026301] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'cmake'}
[0.027719] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'build'}
[0.028668] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/build/joystick_input', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[0.099446] (-) TimerEvent: {}
[0.141953] (joystick_input) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/joystick_input.dir/src/JoystickInput.cpp.o\x1b[0m\n'}
[0.199602] (-) TimerEvent: {}
[0.300194] (-) TimerEvent: {}
[0.400755] (-) TimerEvent: {}
[0.501243] (-) TimerEvent: {}
[0.601744] (-) TimerEvent: {}
[0.702226] (-) TimerEvent: {}
[0.802713] (-) TimerEvent: {}
[0.903218] (-) TimerEvent: {}
[1.003771] (-) TimerEvent: {}
[1.104248] (-) TimerEvent: {}
[1.204868] (-) TimerEvent: {}
[1.305392] (-) TimerEvent: {}
[1.406057] (-) TimerEvent: {}
[1.506721] (-) TimerEvent: {}
[1.607383] (-) TimerEvent: {}
[1.708053] (-) TimerEvent: {}
[1.808726] (-) TimerEvent: {}
[1.909390] (-) TimerEvent: {}
[2.010028] (-) TimerEvent: {}
[2.110712] (-) TimerEvent: {}
[2.211392] (-) TimerEvent: {}
[2.312023] (-) TimerEvent: {}
[2.412606] (-) TimerEvent: {}
[2.513285] (-) TimerEvent: {}
[2.613964] (-) TimerEvent: {}
[2.714581] (-) TimerEvent: {}
[2.815225] (-) TimerEvent: {}
[2.915753] (-) TimerEvent: {}
[3.016214] (-) TimerEvent: {}
[3.116636] (-) TimerEvent: {}
[3.216990] (-) TimerEvent: {}
[3.317414] (-) TimerEvent: {}
[3.417883] (-) TimerEvent: {}
[3.518289] (-) TimerEvent: {}
[3.618655] (-) TimerEvent: {}
[3.719075] (-) TimerEvent: {}
[3.819485] (-) TimerEvent: {}
[3.919979] (-) TimerEvent: {}
[4.020505] (-) TimerEvent: {}
[4.120981] (-) TimerEvent: {}
[4.221377] (-) TimerEvent: {}
[4.321826] (-) TimerEvent: {}
[4.422309] (-) TimerEvent: {}
[4.522685] (-) TimerEvent: {}
[4.623165] (-) TimerEvent: {}
[4.723577] (-) TimerEvent: {}
[4.824036] (-) TimerEvent: {}
[4.924510] (-) TimerEvent: {}
[5.024886] (-) TimerEvent: {}
[5.125394] (-) TimerEvent: {}
[5.225904] (-) TimerEvent: {}
[5.326403] (-) TimerEvent: {}
[5.426918] (-) TimerEvent: {}
[5.527379] (-) TimerEvent: {}
[5.627751] (-) TimerEvent: {}
[5.728267] (-) TimerEvent: {}
[5.828762] (-) TimerEvent: {}
[5.929201] (-) TimerEvent: {}
[6.029544] (-) TimerEvent: {}
[6.130056] (-) TimerEvent: {}
[6.230424] (-) TimerEvent: {}
[6.330919] (-) TimerEvent: {}
[6.431414] (-) TimerEvent: {}
[6.531886] (-) TimerEvent: {}
[6.632354] (-) TimerEvent: {}
[6.732712] (-) TimerEvent: {}
[6.833127] (-) TimerEvent: {}
[6.933607] (-) TimerEvent: {}
[7.034004] (-) TimerEvent: {}
[7.134370] (-) TimerEvent: {}
[7.234852] (-) TimerEvent: {}
[7.335173] (-) TimerEvent: {}
[7.435557] (-) TimerEvent: {}
[7.536086] (-) TimerEvent: {}
[7.636587] (-) TimerEvent: {}
[7.737082] (-) TimerEvent: {}
[7.837545] (-) TimerEvent: {}
[7.937861] (-) TimerEvent: {}
[8.038310] (-) TimerEvent: {}
[8.138627] (-) TimerEvent: {}
[8.239151] (-) TimerEvent: {}
[8.302119] (joystick_input) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable joystick_input\x1b[0m\n'}
[8.339264] (-) TimerEvent: {}
[8.439608] (-) TimerEvent: {}
[8.540165] (-) TimerEvent: {}
[8.615221] (joystick_input) StdoutLine: {'line': b'[100%] Built target joystick_input\n'}
[8.626194] (joystick_input) CommandEnded: {'returncode': 0}
[8.627723] (joystick_input) JobProgress: {'identifier': 'joystick_input', 'progress': 'install'}
[8.640249] (-) TimerEvent: {}
[8.642518] (joystick_input) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/build/joystick_input'], 'cwd': '/home/<USER>/build/joystick_input', 'env': OrderedDict({'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'HOSTNAME': 'c974ef9d5033', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/install/control_input_msgs/lib:/home/<USER>/install/unitree_guide_controller/lib:/home/<USER>/install/ocs2_quadruped_controller/lib:/home/<USER>/install/qpoases_colcon/lib:/home/<USER>/install/ocs2_legged_robot_ros/lib:/home/<USER>/install/ocs2_legged_robot/lib:/home/<USER>/install/ocs2_sqp/lib:/home/<USER>/install/ocs2_sphere_approximation/lib:/home/<USER>/install/ocs2_self_collision/lib:/home/<USER>/install/ocs2_ros_interfaces/lib:/home/<USER>/install/ocs2_centroidal_model/lib:/home/<USER>/install/ocs2_pinocchio_interface/lib:/home/<USER>/install/ocs2_robotic_tools/lib:/home/<USER>/install/ocs2_ipm/lib:/home/<USER>/install/ocs2_ddp/lib:/home/<USER>/install/hpipm_colcon/lib:/home/<USER>/install/ocs2_qp_solver/lib:/home/<USER>/install/ocs2_mpc/lib:/home/<USER>/install/ocs2_oc/lib:/home/<USER>/install/ocs2_core/lib:/home/<USER>/install/ocs2_msgs/lib:/home/<USER>/install/hardware_unitree_sdk2/lib:/home/<USER>/install/gz_quadruped_hardware/lib:/home/<USER>/install/grid_map_sdf/lib:/home/<USER>/install/convex_plane_decomposition_ros/lib:/home/<USER>/install/convex_plane_decomposition/lib:/home/<USER>/install/grid_map_filters_rsl/lib:/home/<USER>/install/convex_plane_decomposition_msgs/lib:/home/<USER>/install/controller_common/lib:/home/<USER>/install/blasfeo_colcon/lib:/usr/local/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/root', 'OLDPWD': '/', 'ROS_PYTHON_VERSION': '3', 'COLCON_PREFIX_PATH': '/home/<USER>/install', 'ROS_DISTRO': 'jazzy', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/usr/local/lib/pkgconfig:', 'NVIDIA_DRIVER_CAPABILITIES': 'all', 'TERM': 'xterm', 'PATH': '/usr/local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/home/<USER>/bin', 'DISPLAY': ':1', 'LANG': 'C.UTF-8', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'ROS_DOMAIN_ID': '1', 'AMENT_PREFIX_PATH': '/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/control_input_msgs:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/opt/ros/jazzy', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'PWD': '/home/<USER>/build/joystick_input', 'LC_ALL': 'C.UTF-8', 'PYTHONPATH': '/home/<USER>/install/control_input_msgs/lib/python3.12/site-packages:/home/<USER>/install/ocs2_msgs/lib/python3.12/site-packages:/home/<USER>/install/convex_plane_decomposition_msgs/lib/python3.12/site-packages:/usr/local/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/home/<USER>/install/control_input_msgs:/home/<USER>/install/x30_description:/home/<USER>/install/unitree_guide_controller:/home/<USER>/install/ocs2_quadruped_controller:/home/<USER>/install/qpoases_colcon:/home/<USER>/install/ocs2_legged_robot_ros:/home/<USER>/install/ocs2_legged_robot:/home/<USER>/install/ocs2_sqp:/home/<USER>/install/ocs2_sphere_approximation:/home/<USER>/install/ocs2_self_collision:/home/<USER>/install/ocs2_ros_interfaces:/home/<USER>/install/ocs2_centroidal_model:/home/<USER>/install/ocs2_pinocchio_interface:/home/<USER>/install/ocs2_robotic_tools:/home/<USER>/install/ocs2_ipm:/home/<USER>/install/ocs2_ddp:/home/<USER>/install/hpipm_colcon:/home/<USER>/install/ocs2_qp_solver:/home/<USER>/install/ocs2_mpc:/home/<USER>/install/ocs2_oc:/home/<USER>/install/ocs2_core:/home/<USER>/install/ocs2_thirdparty:/home/<USER>/install/ocs2_robotic_assets:/home/<USER>/install/ocs2_msgs:/home/<USER>/install/lite3_description:/home/<USER>/install/keyboard_input:/home/<USER>/install/joystick_input:/home/<USER>/install/hardware_unitree_sdk2:/home/<USER>/install/gz_quadruped_playground:/home/<USER>/install/gz_quadruped_hardware:/home/<USER>/install/grid_map_sdf:/home/<USER>/install/convex_plane_decomposition_ros:/home/<USER>/install/convex_plane_decomposition:/home/<USER>/install/grid_map_filters_rsl:/home/<USER>/install/go2_description:/home/<USER>/install/convex_plane_decomposition_msgs:/home/<USER>/install/controller_common:/home/<USER>/install/cgal5_colcon:/home/<USER>/install/blasfeo_colcon:/usr/local:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/opt/ros/jazzy'}), 'shell': False}
[8.653432] (joystick_input) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.653724] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/lib/joystick_input/joystick_input\n'}
[8.658123] (joystick_input) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/install/joystick_input/lib/joystick_input/joystick_input" to ""\n'}
[8.658367] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch\n'}
[8.658556] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input//launch/joystick.launch.py\n'}
[8.658723] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/package_run_dependencies/joystick_input\n'}
[8.658842] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/parent_prefix_path/joystick_input\n'}
[8.658951] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.sh\n'}
[8.659053] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/ament_prefix_path.dsv\n'}
[8.659157] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.sh\n'}
[8.659262] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/environment/path.dsv\n'}
[8.659366] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.bash\n'}
[8.659474] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.sh\n'}
[8.659563] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.zsh\n'}
[8.659645] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/local_setup.dsv\n'}
[8.659735] (joystick_input) StdoutLine: {'line': b'-- Installing: /home/<USER>/install/joystick_input/share/joystick_input/package.dsv\n'}
[8.659858] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/ament_index/resource_index/packages/joystick_input\n'}
[8.659945] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig.cmake\n'}
[8.660020] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/cmake/joystick_inputConfig-version.cmake\n'}
[8.660093] (joystick_input) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/install/joystick_input/share/joystick_input/package.xml\n'}
[8.660673] (joystick_input) CommandEnded: {'returncode': 0}
[8.673550] (joystick_input) JobEnded: {'identifier': 'joystick_input', 'rc': 0}
[8.674598] (-) EventReactorShutdown: {}
